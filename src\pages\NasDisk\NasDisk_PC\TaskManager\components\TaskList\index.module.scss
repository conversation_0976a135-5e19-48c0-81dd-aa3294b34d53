.task_list_container {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* Tab 头部样式 */
.tab_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--background-color);
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.tab_list {
  display: flex;
  gap: 24px;
}

.tab_item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: MiSans W;
  font-size: 14px;
  color: var(--text-color);
  background-color: transparent;
}

.tab_item:hover:not(.active) {
  background-color: #F5F6F7;
}

// .tab_item.error .tab_count {
//   color: #ff4d4f;
// }

/* 确保active状态的优先级最高 */
.tab_item.active {
  background-color: #65CBD0 !important;
  color: #fff !important;
}

.tab_item.active:hover {
  background-color: #65CBD0 !important;
}

.tab_item.active .tab_count {
  color: #fff !important;
}

.tab_item.active .tab_label {
  color: #fff !important;
}

.tab_label {
  font-weight: 500;
  color:#979797;
}

.tab_count {
  font-weight: 400;
  color:#979797;
}

.tab_actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Loading指示器样式 */
.loading_indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--text-color-secondary);
  background-color: var(--background-color-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.loading_dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #65CBD0;
  animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.action_btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #C9CDD4;
  border-radius: 4px;
  background-color: transparent;
  color: rgba(78, 89, 105, 1);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: MiSans W;
  font-size: 14px;
}

.action_btn:hover {
  background-color: var(--hover-background-color);
}

.action_btn img {
  width: 16px;
  height: 16px;
}

/* 内容区域样式 */
.task_content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.task_list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list_header {
  display: grid;
  grid-template-columns: 45% 45% 10%;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
  align-items: center;
}

.header_item {
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 500;
  color: rgba(140, 147, 176, 1);
  opacity: 0.8;
  text-align: left;
}

.list_body {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
}

/* 任务项样式 */
.task_item {
  display: grid;
  grid-template-columns: 45% 45% 10%;
  gap: 16px;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.task_item:hover {
  background-color: var(--hover-background-color);
}

.task_info {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.task_icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.task_icon img {
  width: 24px;
  height: 24px;
}

.task_content {
  flex: 1;
  min-width: 0;
}

.task_name {
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.task_details {
  display: flex;
  gap: 12px;
  font-family: MiSans W;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.6;
}

.task_size {
  font-weight: 400;
}

.task_status {
  font-weight: 400;
}

/* 进度条样式 */
.task_progress {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.task_progress_info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.progress_container {
  width: 100%;
}

.progress_bar {
  width: 100%;
  height: 5px;
  background-color: #E5E5E5;
  border-radius: 5px;
  overflow: hidden;
}

.progress_fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress_fill.进行中 {
  background-color: #32BAC0;
}

.progress_fill.失败存储 {
  background-color: #32BAC0;
}

.progress_fill.已完成 {
  background-color: #32BAC0;
}

.task_speed {
  font-family: MiSans W;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.6;
  text-align: right;
  white-space: nowrap;
}

.task_actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.task_actions .action_btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task_actions .action_btn_two {
  width: 32px;
  height: 32px;
  border: none;
  background-color: var(--background-color);
}

.task_actions .action_btn img {
  width: 16px;
  height: 16px;
}

/* 加载状态 */
.loading_container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading_spinner {
  font-family: MiSans W;
  font-size: 16px;
  color: var(--text-color);
  opacity: 0.6;
}

/* 已完成状态的样式 */
.task_list_container.completed .list_header {
  grid-template-columns: 35% 35% 15% 15%;
}

.task_list_container.completed .task_item {
  grid-template-columns: 35% 35% 15% 15%;
}

.task_storage {
  display: flex;
  align-items: center;
  min-width: 0;
}

.storage_location {
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-color);
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task_size_column {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.file_size {
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-color);
  opacity: 0.8;
}

.task_completed_time {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.completed_time {
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-color);
  opacity: 0.8;
  white-space: nowrap;
}

/* 删除弹窗样式 */
.delete_modal_content {
  font-family: MiSans W;
  font-size: 16px;
  color: var(--text-color);
  line-height: 1.5;
  text-align: center;
  padding: 20px 0;
} 