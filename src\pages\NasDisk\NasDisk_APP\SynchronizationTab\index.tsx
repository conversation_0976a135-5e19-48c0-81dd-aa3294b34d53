import React, { useState, useEffect, useCallback } from 'react';
import {  Toast, Loading, ProgressBar, Checkbox } from 'antd-mobile';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useRequest } from 'ahooks';
import styles from './index.module.scss';
import GuidePanel from '@/pages/NasDisk/NasDisk_APP/components/GuidePanel';
import { controlTask, ControlTaskParams } from '@/api/nasDisk';
import { modalShow } from '@/components/List';
import request from '@/request';
import tipImg from '@/Resources/nasDiskImg/download-automatic.png';
import fileIcon from '@/Resources/nasDiskImg/file-icon.png';
import start from '@/Resources/nasDiskImg/start.png';
import pause from '@/Resources/nasDiskImg/startIcon.png';
import closeIcon from '@/Resources/camMgmtImg/close.png';
import deletes from '@/Resources/camMgmtImg/delete.png';
import deletesDark from '@/Resources/camMgmtImg/deletes-dark.png';
import { PreloadImage } from '@/components/Image';
import { useTheme } from '@/utils/themeDetector';

// 定义同步任务的接口
interface SyncTask {
  id: string;
  name: string;
  status: 'waiting' | 'running' | 'paused' | 'success_waiting' | 'failed' | 'success';
  progress: number;
  localPath: string;
  remotePath: string;
  finish_file_cnt: number;
  total_file_cnt: number;
  createTime: string; // 保持原始时间戳
  lastSyncTime?: string;
  errorMessage?: string;
  handleSize: string;
  totalSize: string;
  src?: string[]; // 添加原始路径数组
}

interface SynchronizationTabProps {
  isVip?: boolean;
  onTasksChange?: (hasAnyTasks: boolean) => void;
  isEditMode?: boolean;
  setIsEditMode?: (isEditMode: boolean) => void;
}

// 格式化时间 - 处理毫秒时间戳
const formatTime = (timestamp: string): string => {
  if (!timestamp) return "";
  const date = new Date(parseInt(timestamp));
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

  // 字节大小转换函数
export const formatBytes = (bytes: string | number): string => {
    const size = typeof bytes === 'string' ? parseInt(bytes) : bytes;
    if (size === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    
    return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

// 获取任务显示名称
const getTaskDisplayName = (task: SyncTask): string => {
  if (task.src && task.src.length > 0) {
    // 获取最后一个路径部分
    const path = task.src[task.src.length - 1];

    // 如果路径包含斜杠，分割并获取最后一个非空部分
    if (path.includes('/')) {
      const pathParts = path.split('/').filter(part => part.length > 0);
      return pathParts.length > 0 ? pathParts[pathParts.length - 1] : path;
    }

    // 移除前导斜杠"/"
    return path.startsWith('/') ? path.substring(1) : path;
  }

  // 如果没有src，从name中提取
  if (task.name && task.name.includes('/')) {
    const pathParts = task.name.split('/').filter(part => part.length > 0);
    return pathParts.length > 0 ? pathParts[pathParts.length - 1] : task.name;
  }

  return task.name || `任务${task.id}`;
};

// 计算任务的更新时间显示
const getTaskUpdateTime = (task: SyncTask) => {
  const timestamp = task.createTime;
  if (!timestamp) return "";

  const taskTime = parseInt(timestamp);
  const now = Date.now();
  const diffMinutes = Math.floor((now - taskTime) / (60 * 1000));

  if (diffMinutes < 1) return "刚刚更新";
  if (diffMinutes < 60) return `${diffMinutes}分钟前更新`;

  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24) return `${diffHours}小时前更新`;

  const diffDays = Math.floor(diffHours / 24);
  if (diffDays < 30) return `${diffDays}天前更新`;

  return formatTime(timestamp);
};

export default function SynchronizationTab({ isVip, onTasksChange, isEditMode: externalIsEditMode, setIsEditMode: externalSetIsEditMode }: SynchronizationTabProps) {
  const history = useHistory();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  // 同步任务列表状态
  const [syncTasks, setSyncTasks] = useState<SyncTask[]>([]);
  const [syncTasksLoading, setSyncTasksLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 编辑模式相关状态 - 使用外部传入的状态或内部状态
  const isEditMode = externalIsEditMode !== undefined ? externalIsEditMode : false;
  const setIsEditMode = externalSetIsEditMode || (() => {});
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [longPressTimeout, setLongPressTimeout] = useState<NodeJS.Timeout | null>(null);



  // 映射任务状态
  const mapTaskStatus = (status: string): SyncTask['status'] => {
    switch (status) {
      case "running":
        return "running";
      case "success_waiting":
        return "success_waiting";
      case "failed":
        return "failed";
      case "paused":
        return "paused";
      case "waiting":
        return "waiting";
      default:
        return "waiting";
    }
  };

  // 获取同步任务数据
  const fetchSyncTasks = useCallback(async (showLoading: boolean = false) => {
    // 只有在初始加载且有任务时才显示加载状态，轮询时保持静默
    if (showLoading && syncTasks.length > 0) {
      setSyncTasksLoading(true);
    }

    try {
      // 获取所有自动上传任务
      const response = await request.post("/taskcenter/get_taskinfo", {
        selector: [
          {
            key: "module",
            value: ["bpan"],
          },
          {
            key: "type",
            value: ["active"],
          },
          {
            key: "action",
            value: ["auto_upload"],
          },
        ],
      },
        { showLoading: false }
      );

      // 处理任务
      if (response?.data?.info) {
        const tasks = response.data.info;
        // 按状态排序：running > paused > waiting > success > 其他
        const sortedTasks = tasks.sort((a: any, b: any) => {
          const statusOrder: Record<string, number> = {
            running: 0,
            paused: 1,
            waiting: 2,
            success: 3,
            failed: 4,
            cancelled: 5,
          };
          return (statusOrder[a.status] || 99) - (statusOrder[b.status] || 99);
        });

        // 转换为SyncTask格式
        const syncTasks: SyncTask[] = sortedTasks.map((task: any) => ({
          id: task.task_id,
          name: task.src?.[task.src.length - 1] || `任务${task.task_id}`,
          status: mapTaskStatus(task.status),
          progress: task.detail?.progress || 0,
          localPath: task.src?.join("/") || "",
          remotePath: task.dst || "",
          fileCount: task.detail?.total_files || 0,
          syncedCount: task.detail?.handled_files || 0,
          createTime: task.create_time || "", // 保持原始时间戳
          lastSyncTime: task.done_time || "",
          handleSize: task.detail?.handle_size || "0",
          totalSize: task.detail?.total_size || "0",
          src: task.src || [], // 保存原始路径数组
        }));

        setSyncTasks(syncTasks);
      } else {
        setSyncTasks([]);
      }
    } catch (error) {
      console.error("获取同步任务失败:", error);
      setSyncTasks([]);
    } finally {
      setSyncTasksLoading(false);
      setIsInitialized(true);
    }
  }, [syncTasks.length]);

  // 使用useRequest处理任务控制
  const { run: runControlTask } = useRequest(
    (params: ControlTaskParams) => controlTask(params),
    {
      manual: true,
      onSuccess: (result, params) => {
        if (result.code === 0) {
          const commandText: Record<string, string> = {
            pause: "暂停",
            continue: "启动",
            cancel: "取消",
            restart: "重启",
          };
          const command = params[0].command;
          Toast.show({
            content: `任务已${commandText[command] || "操作成功"}`,
            duration: 2000,
          });
          // 重新获取任务列表
          fetchSyncTasks(true);
        } else {
          Toast.show({
            content: result.result || "操作失败",
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("任务操作失败:", error);
        Toast.show({
          content: "操作失败，请重试",
          duration: 2000,
        });
      },
    }
  );

  // 初始化数据
  useEffect(() => {
    if (isVip) {
      fetchSyncTasks(true);
    } else {
      setIsInitialized(true);
    }
  }, [isVip, fetchSyncTasks]);

  // 根据任务数量设置轮询
  useEffect(() => {
    // 只有当有任务且是VIP用户时才设置轮询
    let timer: NodeJS.Timeout | null = null;

    if (syncTasks.length > 0 && isVip) {
      timer = setInterval(fetchSyncTasks, 5000); // 每5秒轮询任务
    }

    // 通知父组件任务状态变化
    if (onTasksChange) {
      onTasksChange(syncTasks.length > 0);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [syncTasks.length, isVip, fetchSyncTasks, onTasksChange]);

  // 处理新增自动上传任务
  const handleAddTask = () => {
    if (syncTasks.length >= 5) {
      Toast.show({
        content: '最多选择五个文件夹',
        duration: 2000,
      });
      return;
    }
    
    // 获取已有任务的文件夹路径列表
    const existingFolderPaths = syncTasks.map(task => {
      if (task.src && task.src.length > 0) {
        // 获取最后一个路径部分作为文件夹路径
        return task.src[task.src.length - 1];
      }
      return '';
    }).filter(path => path !== '');
    
    history.push({
      pathname: "/baiduNetdisk_app/synchronization",
      state: {
        existingTaskCount: syncTasks.length,
        existingFolderPaths: existingFolderPaths
      }
    });
  };

  // 处理开通会员
  const handleUpgradeToVip = () => {
    modalShow(
      "会员权益",
      "该功能为网盘NAS会员权益，是否要开通？",
      (modal) => {
        // 确认开通，跳转到会员页面
        modal.destroy();
        history.push(`${path}/members`);
      },
      () => {
        // 取消操作
        console.log("用户取消开通会员");
      },
      false,
      {
        position: 'bottom',
        okBtnText: '开通会员',
        cancelBtnText: '取消',
        okBtnStyle: { backgroundColor: '#402C00', color: '#E2AE1E' },
        cancelBtnStyle: { backgroundColor: '#F0F0F0', color: '#000' }
      }
    );
  };

  // 处理任务操作
  const handleTaskAction = (taskId: string, action: 'pause' | 'resume' | 'delete') => {
    if (action === 'delete') {
      handleDeleteTask(taskId);
      return;
    }

    try {
      // 根据动作映射到API命令
      const commandMap: Record<string, 'pause' | 'continue'> = {
        pause: 'pause',
        resume: 'continue',
      };

      const command = commandMap[action];
      if (command) {
        runControlTask({
          task_id: [taskId],
          command: command,
        });
      }
    } catch (error) {
      console.error("操作任务失败:", error);
      Toast.show({
        content: "操作失败，请重试",
        duration: 2000,
      });
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId: string) => {
    modalShow(
      "删除自动上传任务",
      "仅删除自动上传任务，不会删除已上传文件",
      (modal) => {
        // 确认删除
        runControlTask({
          task_id: [taskId],
          command: "cancel",
        });
        modal.destroy();
      },
      () => {
        // 取消删除
        console.log("取消删除任务");
      },
      false,
      {
        position: "center",
        okBtnText: "确定",
        cancelBtnText: "取消",
        contentHeight: "auto",
      }
    );
  };

  // 获取状态颜色
  const getStatusColor = (status: SyncTask['status']) => {
    switch (status) {
      case 'success_waiting':
        return '#32BAC0';
      case 'running':
        return '#32BAC0';
      case 'waiting':
        return '#32BAC0';
      case 'failed':
        return '#ff4d4f';
      case 'paused':
        return '#fa8c16';
      default:
        return '#666';
    }
  };

  // 空状态UI - 非VIP用户
  const renderNonVipState = () => (
    <GuidePanel
      imageSrc={tipImg}
      imageAlt=""
      title="智能存储自动上传至百度网盘"
      description="智能存储的文件设置为自动上传后，智能存储会定期（非休眠期间每小时）检查此文件夹是否有新文件加入，如果有新文件，将会自动把新增文件上传到智能存储。"
      buttonText="开通会员，享受自动上传特权"
      onButtonClick={handleUpgradeToVip}
      buttonClassName={styles.vipButton}
    />
  );

  // 空状态UI - VIP用户无任务
  const renderVipEmptyState = () => (
    <GuidePanel
      imageSrc={tipImg}
      imageAlt=""
      title="智能存储自动上传至百度网盘"
      description="智能存储的文件设置为自动上传后，智能存储会定期（非休眠期间每小时）检查此文件夹是否有新文件加入，如果有新文件，将会自动把新增文件上传到智能存储。"
      buttonText="新增自动上传任务"
      onButtonClick={handleAddTask}
      buttonClassName={styles.vipActiveButton}
    />
  );

  // 处理长按开始
  const handleLongPressStart = (taskId: string) => {
    if (!setIsEditMode) return;
    const timeout = setTimeout(() => {
      setIsEditMode(true);
      setSelectedTasks([]); // 进入编辑模式时不选中任何任务
    }, 500); // 500ms长按触发
    setLongPressTimeout(timeout);
  };

  // 处理长按结束
  const handleLongPressEnd = () => {
    if (longPressTimeout) {
      clearTimeout(longPressTimeout);
      setLongPressTimeout(null);
    }
  };

  // 处理退出编辑模式
  const handleExitEditMode = () => {
    if (!setIsEditMode) return;
    setIsEditMode(false);
    setSelectedTasks([]);
  };

  // 处理选择/取消选择任务
  const handleToggleSelectTask = (taskId: string) => {
    if (selectedTasks.includes(taskId)) {
      setSelectedTasks(selectedTasks.filter((id) => id !== taskId));
    } else {
      setSelectedTasks([...selectedTasks, taskId]);
    }
  };

  // 处理全选/取消全选
  const handleToggleSelectAll = () => {
    if (selectedTasks.length === syncTasks.length) {
      // 如果已全选，则取消全选
      setSelectedTasks([]);
    } else {
      // 否则全选
      setSelectedTasks(syncTasks.map((task) => task.id));
    }
  };

  // 处理批量删除任务
  const handleBatchDelete = () => {
    if (selectedTasks.length === 0) {
      Toast.show({
        content: "请选择要删除的任务",
        duration: 2000,
      });
      return;
    }

    // 显示删除确认弹窗
    modalShow(
      "删除自动上传任务",
      "仅删除自动上传任务，不会删除已上传文件",
      (modal) => {
        // 使用控制任务接口取消任务
        try {
          runControlTask({
            task_id: selectedTasks,
            command: "cancel",
          });
          handleExitEditMode();
        } catch (error) {
          console.log("error: ", error);
          Toast.show({
            content: "删除任务失败，请重试",
            duration: 2000,
          });
        }
        modal.destroy();
      },
      () => {
        // 取消删除
        console.log("取消删除任务");
      },
      false,
      {
        position: "bottom",
        okBtnText: "确定",
        cancelBtnText: "取消",
        okBtnStyle: { backgroundColor: "#32BAC0", color: "#fff" },
      }
    );
  };

  // 编辑模式UI
  const renderEditMode = () => (
    <div className={styles.editModeContainer}>
      <div className={styles.editHeader}>
        <div className={styles.closeIconContainer} onClick={handleExitEditMode}>
          <img src={closeIcon} alt="关闭" className={styles.closeIcon} />
        </div>
      </div>
      <div className={styles.editTitle}>
        删除任务
        <Checkbox
          checked={
            selectedTasks.length === syncTasks.length &&
            syncTasks.length > 0
          }
          onChange={handleToggleSelectAll}
          className={styles.selectAllCheckbox}
        />
      </div>

      <div className={styles.editContent}>
        {syncTasks.map((task) => (
          <div
            key={task.id}
            className={`${styles.taskItem} ${selectedTasks.includes(task.id) ? styles.selected : ""
              }`}
            onClick={() => handleToggleSelectTask(task.id)}
          >
            <div className={styles.taskMainInfo}>
              <div className={styles.taskIcon}>
                <PreloadImage
                  src={fileIcon}
                  alt=""
                  style={{ width: 40, height: 40 }}
                />
              </div>
              <div className={styles.taskContent}>
                <div className={styles.taskName}>{getTaskDisplayName(task)}</div>
                <div className={styles.progressContainer}>
                  <ProgressBar
                    percent={task.progress}
                    text={false}
                    className={styles.taskProgress}
                    style={{
                      '--fill-color': getStatusColor(task.status),
                    } as React.CSSProperties}
                  />
                </div>
                <div className={styles.statusContainer}>
                  <div className={styles.taskStatus}>
                    {task.status === 'waiting' ? `等待中` :
                      task.status === 'running' ? `${formatBytes(task.handleSize)}/${formatBytes(task.totalSize)}` :
                        task.status === 'success_waiting' ? '已完成' :
                          task.status === 'paused' ? '已暂停' :
                            task.status === 'failed' ? '同步失败' :
                              `${formatBytes(task.handleSize)}/${formatBytes(task.totalSize)}`}
                  </div>
                  {task.status !== 'success_waiting' && (
                    <div className={styles.fileCount}>
                      {task.finish_file_cnt}/{task.total_file_cnt}
                    </div>
                  )}
                </div>
              </div>
              <div
                className={styles.checkboxContainer}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handleToggleSelectTask(task.id);
                }}
              >
                <Checkbox
                  checked={selectedTasks.includes(task.id)}
                  className={styles.taskCheckbox}
                />
              </div>
            </div>
            <div className={styles.taskSubInfo}>
              <div className={styles.taskPath}>{task.remotePath}</div>
              <div className={styles.taskTime}>{getTaskUpdateTime(task)}</div>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.deleteFooter}>
        <div
          className={`${styles.deleteButton} ${selectedTasks.length === 0 ? styles.disabled : ""
            }`}
          onClick={selectedTasks.length > 0 ? handleBatchDelete : undefined}
        >
          <img
            alt=""
            src={isDarkMode ? deletesDark : deletes}
            className={styles.photoEditDeleteIcon}
          />
          <span>删除</span>
        </div>
      </div>
    </div>
  );

  // 任务列表UI
  const renderTaskList = () => (
    <div className={styles.taskContainer}>
      <div className={styles.taskHeader}>
        <div className={styles.taskHeaderTitle}>
          自动上传的文件夹
          <span className={styles.taskCount}>({syncTasks.length}/5)</span>
        </div>
        <div
          className={`${styles.newBadge} ${syncTasks.length >= 5 ? styles.disabled : ''}`}
          onClick={syncTasks.length >= 5 ? undefined : handleAddTask}
        >
          新增
        </div>
      </div>

      {syncTasksLoading ? (
        <div className={styles.loading}>加载中...</div>
      ) : (
        <div className={styles.taskList}>
          {syncTasks.map(task => (
            <div
              key={task.id}
              className={styles.taskItem}
              onTouchStart={() => handleLongPressStart(task.id)}
              onTouchEnd={handleLongPressEnd}
              onTouchMove={handleLongPressEnd}
            >
              <div className={styles.taskMainInfo}>
                <div className={styles.taskIcon}>
                  <PreloadImage
                    src={fileIcon}
                    alt=""
                    style={{ width: 40, height: 40 }}
                  />
                </div>
                <div className={styles.taskContent}>
                  <div className={styles.taskName}>{getTaskDisplayName(task)}</div>
                  <div className={styles.progressContainer}>
                    <ProgressBar
                      percent={task.progress}
                      text={false}
                      className={styles.taskProgress}
                      style={{
                        '--fill-color': getStatusColor(task.status),
                      } as React.CSSProperties}
                    />
                  </div>
                  <div className={styles.statusContainer}>
                    <div className={styles.taskStatus}>
                      {task.status === 'waiting' ? `等待中` :
                        task.status === 'running' ? `${formatBytes(task.handleSize)}/${formatBytes(task.totalSize)}` :
                          task.status === 'success_waiting' ? '已完成' :
                            task.status === 'paused' ? '已暂停' :
                              task.status === 'failed' ? '同步失败' :
                                `${formatBytes(task.handleSize)}/${formatBytes(task.totalSize)}`}
                    </div>
                    {task.status !== 'success_waiting' && (
                      <div className={styles.fileCount}>
                        {task.finish_file_cnt}{task.total_file_cnt}
                      </div>
                    )}
                  </div>
                </div>
                <div className={styles.taskActions}>

                  <span
                    className={styles.actionButton}
                    onClick={() => handleTaskAction(task.id, task.status === 'running' || task.status === 'waiting' ? 'pause' : 'resume')}
                  >
                    {(task.status === 'waiting' || task.status === 'running') ? (
                      <img alt="" src={pause} className={styles.pauseIcon} />
                    ) : (
                      <img alt="" src={start} className={styles.playIcon} />
                    )}
                  </span>

                </div>
              </div>
              <div className={styles.taskSubInfo}>
                <div className={styles.taskPath}>{task.remotePath}</div>
                <div className={styles.taskTime}>{getTaskUpdateTime(task)}</div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  // 如果正在初始加载，显示加载状态
  if (!isInitialized) {
    return (
      <div className={styles.loadingContainer}>
        <Loading />
        <span>加载中...</span>
      </div>
    );
  }

  // 如果处于编辑模式，显示编辑模式UI
  if (isEditMode) {
    return renderEditMode();
  }

  // 根据VIP状态和任务数量决定显示哪个UI
  if (!isVip) {
    return renderNonVipState();
  }

  return syncTasks.length > 0 ? renderTaskList() : renderVipEmptyState();
} 