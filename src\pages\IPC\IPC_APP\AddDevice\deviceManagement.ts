import { useState } from "react";
import { useRequest } from "ahooks";
import {
  listAllCamera,
  BasicCameraInfo,
} from "@/api/ipc";

// 设备数据类型定义
export type CameraDevice = {
  id: string;
  name: string;
  model: string;
  ip: string;
  mac: string;
  thumbnail: string;
  selected: boolean;
};

// 状态类型定义
export type FetchState = {
  status: "loading" | "error" | "empty" | "success";
  errorType?: "network" | "device";
  devices: CameraDevice[];
};

// 设备管理Hook
export const useDeviceManagement = (thumbnailSrc: string) => {
  const [fetchState, setFetchState] = useState<FetchState>({
    status: "loading",
    devices: [],
  });

  const { loading } = useRequest(listAllCamera, {
    manual: false,
    onSuccess: (result) => {
      console.log("获取摄像头列表成功:", result);
      const cameraList = result.data.camera || [];

      if (cameraList.length === 0) {
        setFetchState({
          status: "empty",
          devices: [],
        });
      } else {
        const devices = cameraList.map((camera: BasicCameraInfo) => ({
          id: camera.did,
          name: camera.name,
          model: camera.model,
          ip: camera.ip,
          mac: camera.mac,
          thumbnail: thumbnailSrc,
          selected: false,
        }));

        setFetchState({
          status: "success",
          devices,
        });
      }
    },
    onError: (error) => {
    },
  });

  // 切换单个设备选中状态
  const toggleSelect = (id: string) => {
    setFetchState((prev) => ({
      ...prev,
      devices: prev.devices.map((device) =>
        device.id === id ? { ...device, selected: !device.selected } : device
      ),
    }));
  };

  // 全选/取消全选功能
  const toggleSelectAll = () => {
    const allSelected = fetchState.devices.every((device) => device.selected);
    setFetchState((prev) => ({
      ...prev,
      devices: prev.devices.map((device) => ({
        ...device,
        selected: !allSelected,
      })),
    }));
  };

  // 重试加载
  const handleRetry = () => {
    setFetchState({ status: "loading", devices: [] });
    // 模拟重试
    setTimeout(() => {
      // processMockData();
    }, 1000);
  };

  // 获取已选设备数量
  const selectedCount = fetchState.devices.filter((d) => d.selected).length;

  // 获取已选设备列表
  const selectedDevices = fetchState.devices.filter(
    (device) => device.selected
  );

  return {
    fetchState,
    setFetchState,
    toggleSelect,
    toggleSelectAll,
    handleRetry,
    selectedCount,
    selectedDevices,
    loading,
  };
};
