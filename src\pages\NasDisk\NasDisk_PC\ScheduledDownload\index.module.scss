.scheduledDownload {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  padding: 20px 24px;
}

.loading {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #888;
}

.taskContainer {
  width: 100%;
}

.taskHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.headerTitle {
  font-size: 16px;
  font-weight: 500;
  color: rgba(151, 151, 151, 1);
}

.headerActions {
  display: flex;
  align-items: center;
  :global{
    :where(.css-dev-only-do-not-override-1d4w9r2).ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover{
      border-color: #fff;
    }
  }
}

.addButton {
  background-color: #fff;
  border-color: #fff;
  font-size: 14px;
  height: 32px;
  color: rgba(97, 163, 255, 1);
  
  // &:hover, &:focus {
  //   background-color: #28a0a5;
  //   border-color: #28a0a5;
  // }
  
  &:disabled {
    background-color: #b5b5b5;
    border-color: #b5b5b5;
  }
}

.taskList {
  display: flex;
  flex-direction: column;
}

.taskItem {
  display: flex;
  align-items: center;
  background-color: rgba(247, 247, 247, 1);
  border-radius: 10px;
  padding: 10px 24px;
  margin-top: 20px;
  
  &:first-child {
    margin-top: 0;
  }
}

.taskLeftSection {
  display: flex;
  align-items: center;
  width: 200px;
  flex-shrink: 0;
}

.taskIcon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  flex-shrink: 0;
}

.folderIcon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.taskName {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.taskMiddleSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 20px;
}

.taskStatus {
  font-size: 14px;
  color: #333;
}
.taskAll{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progressWrapper {
  :global(.ant-progress-inner) {
    background-color: #f0f0f0;
  }
  
  :global(.ant-progress-bg) {
    background-color: #32BAC0;
  }
}
.pathBox{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.taskPath {
  font-size: 12px;
  color: #999;
}

.taskRightSection {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 100px;
  flex-shrink: 0;
}

.progressCounter {
  font-size: 14px;
  color: #333;
}

.actionButtons {
  display: flex;
  gap: 30px;
  align-items: center;
}

.controlIcon {
  cursor: pointer;
}

.placeholderIcon {
  width: 24px;
  height: 24px;
  visibility: hidden;
}

.deleteImg {
  cursor: pointer;
}

.deleteConfirmContent {
  font-size: 16px;
  text-align: center;
  padding: 10px 0 16px 0;
  color: #333;
}

.updateTime {
  font-size: 12px;
  color: #999;
}
