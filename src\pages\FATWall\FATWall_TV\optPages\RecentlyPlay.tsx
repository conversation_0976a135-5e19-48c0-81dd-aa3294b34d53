import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './index.module.scss';
import TVFocusable, { FocusableElement } from '../TVFocus';
import { PreloadImage } from '@/components/Image';
import playIcon from '@/Resources/player/play.png';
import collect_icon_tv from '@/Resources/filmWall/collect_icon_tv.png';
import collected_icon_tv from '@/Resources/filmWall/collected_icon_tv.png';
import notWatch_icon_tv from '@/Resources/filmWall/notWatch_icon_tv.png';
import watched_icon_tv from '@/Resources/filmWall/watched_icon_tv.png';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { collect, collectEpisode, getRecentlyWatched, markWatched, markWatchedEpisode, RecentlyPlayedResponse, getMediaFiles, MediaFileResponse } from '@/api/fatWall';
import { defaultPageParam } from '../../FATWall_APP/Recently';
import { formatTimeAgo } from '../../FATWall_APP/Recently/RecentlyPlay';
import ErrorComponentTV from '../Error';
import { useLibraryListTV } from '..';
import { Toast } from '@/components/Toast/manager';
import { playVideo } from '@/api/fatWallPlayer';

export interface IDescribe {
    media_id: number;
    file_id?: number;
    flag: string[];
    title: string;
    describe: string;
    // score: string;
    url: string;
    name: string;
    backgroundImage: string;
    percentage: string;
    movieDuration: string // 影片时长
    time: string // 几天前观看
    favourite: number; // 是否收藏
    seen: number; // 是否观看
}

interface IMovieCardTV {
    focusableId: string;
    focusableRow: number;
    focusableCol: number;
    callback: () => void;
    cover: string;
    playTime: string;
    movieTitle: string;
    time: string;
    getCurrentItem?: (item: FocusableElement, keyEvent: KeyboardEvent) => void;
    type: 'play' | 'add'
}

// 计算影片时长，格式化成时分秒的字符串形式
export const duration2time = (duration: number) => {
    const hour = Math.floor(duration / 3600);
    const min = Math.floor((duration % 3600) / 60);
    const second = duration % 60;
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${pad(hour)}:${pad(min)}:${pad(second)}`;
}

export const MovieCardTV = (props: IMovieCardTV) => {
    const { callback, focusableRow, focusableCol, focusableId, cover, playTime, movieTitle, time, getCurrentItem, type } = props;

    return (
        <div className={styles['nasTV_index_desc_recentlyWatched_list_item']}>
            <TVFocusable onClick={callback} id={focusableId} row={focusableRow} col={focusableCol} currentItem={getCurrentItem} className={styles['nasTV_index_desc_recentlyWatched_list_item_cover']}>
                <div className={styles.nasTV_index_desc_recentlyWatched_list_item_cover_item}>
                    {
                        cover !== '' ? <PreloadImage src={cover} alt={'movie'} /> : <div className={styles.nasTV_index_desc_recentlyWatched_list_item_cover_item_tempImg}></div>
                    }
                    <div className={styles['nasTV_index_desc_recentlyWatched_list_item_progress']}>
                        <div className={styles['nasTV_index_desc_recentlyWatched_list_item_progressBar']} style={{ width: `${playTime}%` }}></div>
                    </div>
                </div>
            </TVFocusable>
            <span className={styles['nasTV_index_desc_recentlyWatched_list_item_titleSpan']}>{movieTitle}</span>
            <span className={styles['nasTV_index_desc_recentlyWatched_list_item_continueTitle']}>{`${time}前${type === 'add' ? '添加' : '观看'}`}</span>
        </div>
    )
}

const RecentlyPlay = () => {
    const [acKey, setAcKey] = useState<number>(0);
    const [moveAcKey, setMoveAcKey] = useState<string>('');
    const scrollContainerRef = useRef<HTMLDivElement>(null); // 滚动容器引用
    const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParam);
    const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam);
    const [isError, setIsError] = useState<boolean>(false);
    const [watchRs, setWatchRs] = useState<RecentlyPlayedResponse>({ files: [], count: 0 });
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

    // 加载更多的必要参数
    const loaderRef = useRef<HTMLDivElement>(null);

    const { runAsync } = useRequest(getRecentlyWatched, { manual: true }); // 获取最近观看的电影列表数据

    // 获取媒体文件列表的API调用
    const { run: runGetMediaFiles } = useRequest(
        getMediaFiles,
        {
            manual: true,
            onSuccess: (res: { code: number; data: MediaFileResponse }) => {
                if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
                    console.log('TV端获取到完整剧集列表:', res.data.files);
                    handleStartPlay(res.data.files, currentPlayItem);
                }
            },
            onError: (error) => {
                console.error('TV端获取媒体文件列表失败:', error);
                Toast.show('获取剧集列表失败');
            },
        }
    );

    const initData = useCallback(async (callback: (v: RecentlyPlayedResponse) => void) => {
        const res = await runAsync(pageOptRef.current).catch(e => console.log(`获取最近观看的电影列表数据失败: ${e}`));
        if (res && res.code === 0 && res.data) {
            if (res.data.count < pageOptRef.current.limit) setHasMore(false);
            callback(res.data);
            setIsError(false);
            return;
        }
        setIsError(true);
    }, [runAsync])

    // 初始化数据加载逻辑
    useEffect(() => {
        initData((v) => setWatchRs(v));
    }, [initData])

    // 滚动到底部加载更多数据
    const [inViewport] = useInViewport(loaderRef, {
        threshold: 0.2,
    })

    useUpdateEffect(() => {
        if (inViewport) {
            setPageOpt((prev) => {
                return { ...prev, offset: prev.offset + prev.limit }
            })
        }
    }, [inViewport])

    // 翻页逻辑
    useUpdateEffect(() => {
        pageOptRef.current = pageOpt;
        initData((v) => setWatchRs(p => ({ ...p, files: [...p.files, ...v.files], count: p.count + v.count })));
    }, [pageOpt, initData])

    // 重置
    const clearAndRefresh = useCallback(() => {
        setHasMore(true);
        setWatchRs({ files: [], count: 0 });

        // 重置分页参数，重新加载数据
        setPageOpt(defaultPageParam);
        if (pageOptRef.current) {
            const { limit, offset } = pageOptRef.current;
            if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
                initData((v) => setWatchRs(v));
            }
        }
        pageOptRef.current = defaultPageParam;
    }, [initData])

    // 处理开始播放
    const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
        if (!mediaFiles || mediaFiles.length === 0) {
            Toast.show('暂无可播放的文件');
            return;
        }

        // 构建TV端videoList数组，参考APP端逻辑
        const videoList = mediaFiles.map(file => ({
            path: file.path,
            media_id: playItem.media_id?.toString() || '0',
            file_id: file.file_id.toString(),
            duration: file.duration || 0, // 视频时长
            position: file.last_play_point || 0, // 断点信息
            isCompelete: file.seen, // 是否完整播放，转换为boolean
            audioIndex: file.audio_index || 0, // 音轨信息，默认为0
            subtitlePath: file.subtitle_path || '', // 字幕路径
            subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
            subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
        }));

        // 通过当前项的file_id在列表中找到索引位置
        let playIndex = 0;
        if (playItem.file_id) {
            const targetIndex = mediaFiles.findIndex(file => file.file_id === playItem.file_id);
            if (targetIndex !== -1) {
                playIndex = targetIndex;
                console.log(`TV端Recently播放：找到file_id(${playItem.file_id})对应的索引位置：${targetIndex}`);
            } else {
                console.log(`TV端Recently播放：未找到file_id(${playItem.file_id})对应的文件，将播放第一集`);
            }
        }

        // 调用TV端视频播放接口
        playVideo(videoList, playIndex, (res) => {
            if (res.code === 0) {
                Toast.show('开始播放');
            } else {
                Toast.show(`播放失败: ${res.msg}`);
            }
        }).catch((error) => {
            Toast.show(error.message || '播放失败');
        });

        // 清空当前播放项目状态
        setCurrentPlayItem(null);
    }, []);

    const movieHistory: IDescribe[] = useMemo(() => {
        return watchRs.files.map((file) => {
            const time = formatTimeAgo(file.last_seen_time || 0);
            const poster = file.poster ? file.poster.length > 0 ? file.poster.length > 1 ? file.poster[1] : file.poster[0] : '' : ''; // 数组0索引为封面图，1索引为海报
            return {
                ...file,
                name: file.media_name, title: file.media_name, flag: [`${file.resolution}`, `${file.hdr}`, `${file.audio_codec}`],
                percentage: `${file.last_seen_percent}`, favourite: file.favourite, seen: file.seen, time: time, describe: file.brief,
                movieDuration: duration2time(file.duration), backgroundImage: poster, url: poster
            }
        })
    }, [watchRs.files])

    const history = useHistory();
    const { path } = useRouteMatch();

    // 处理播放按钮点击
    const handlePlayClick = useCallback(() => {
        const currentItem = movieHistory[acKey];
        if (!currentItem) {
            Toast.show('当前没有选中的项目');
            return;
        }

        console.log('TV端点击播放:', currentItem);
        
        if (!currentItem.media_id) {
            Toast.show('缺少媒体ID，无法播放');
            return;
        }

        // 调用接口获取完整的剧集列表
        runGetMediaFiles({
            lib_id: 0, // 根据需求设置为0
            media_id: currentItem.media_id
        });

        // 保存当前点击的项目信息，用于后续播放
        setCurrentPlayItem(currentItem);
    }, [movieHistory, acKey, runGetMediaFiles]);

    const getCurrentItem = useCallback((item: FocusableElement, e) => {
        setMoveAcKey(item.id);
    }, [])

    useUpdateEffect(() => {
        if (!scrollContainerRef.current || movieHistory.length === 0) return; // 确保引用已经初始化
        const scCont = scrollContainerRef.current;
        if (moveAcKey === `tv-id-recentlyPlay-${movieHistory[0].name}`) {
            // scCont.scrollLeft = 0; // 将滚动条位置设置为最左侧

            requestAnimationFrame(() => {
                scCont.scrollTo({
                    left: 0,
                    behavior: 'smooth'
                });
            });
        }
        if (moveAcKey === `tv-id-recentlyPlay-more-movie`) {
            // scCont.scrollLeft = scCont.scrollWidth - scCont.clientWidth; // 将滚动条位置设置为最右侧

            requestAnimationFrame(() => {
                scCont.scrollTo({
                    left: scCont.scrollWidth - scCont.clientWidth,
                    behavior: 'smooth'
                });
            });
        }
    }, [moveAcKey, movieHistory, movieHistory.length])

    const playedByMovie = useCallback(async (seen: number) => {
        if (movieHistory.length > 0 && movieHistory[acKey]) {
            const res = await markWatchedEpisode({ file_id: movieHistory[acKey].file_id || 0, seen: seen });


            if (res && res.code === 0) {
                Toast.show(`${seen ? '已标记' : '取消标记'}观看`);
                clearAndRefresh();
            }
        }
    }, [acKey, clearAndRefresh, movieHistory])

    const likeByMovie = useCallback(async (favourite: number) => {
        if (movieHistory.length > 0 && movieHistory[acKey]) {
            const res = await collectEpisode({ file_id: movieHistory[acKey].file_id || 0, favourite: favourite });

            if (res && res.code === 0) {
                Toast.show(`${favourite ? '已标记' : '取消标记'}喜欢`);
                clearAndRefresh();
            }
        }
    }, [acKey, clearAndRefresh, movieHistory])

    const libs = useLibraryListTV();

    return (
        <ErrorComponentTV isError={isError} hasContent={movieHistory.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
            text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
            {
                movieHistory[acKey] ? (
                    <div className={styles['nasTV_index_container']} style={{
                        backgroundImage: `url(${movieHistory[acKey].backgroundImage})`
                        , backgroundRepeat: 'no-repeat', backgroundSize: "cover", backgroundPosition: "center"
                    }}>
                        <div className={styles['nasTV_index_desc']}>
                            <div className={styles['nasTV_index_desc_title']}>
                                {movieHistory[acKey].title}
                            </div>
                            <div className={styles.nasTV_index_desc_flag_container}>
                                {
                                    movieHistory[acKey].flag.map((it) => (
                                        <div key={it} className={styles['nasTV_index_desc_flag']}>
                                            {it}
                                        </div>
                                    ))
                                }
                            </div>
                            <div className={styles['nasTV_index_desc_describe']}>
                                {movieHistory[acKey].describe}
                            </div>
                            <div className={styles.nasTV_index_desc_btns}>
                                <TVFocusable id={`tv-id-recentlyPlay-play`} row={1} col={1} className={styles.nasTV_index_btns_play_item} onClick={handlePlayClick}>
                                    <PreloadImage src={playIcon} alt='play' />
                                    {`播放 ${movieHistory[acKey].movieDuration}`}
                                </TVFocusable>
                                <TVFocusable id={`tv-id-recentlyPlay-like`} row={1} col={2} className={styles.nasTV_index_btns_item} onClick={() => likeByMovie(movieHistory[acKey].favourite ? 0 : 1)}>
                                    <PreloadImage src={movieHistory[acKey].favourite ? collected_icon_tv : collect_icon_tv} alt='like' />
                                </TVFocusable>
                                <TVFocusable id={`tv-id-recentlyPlay-played`} row={1} col={3} className={styles.nasTV_index_btns_item} onClick={() => playedByMovie(movieHistory[acKey].seen ? 0 : 1)}>
                                    <PreloadImage src={movieHistory[acKey].seen ? watched_icon_tv : notWatch_icon_tv} alt='played' />
                                </TVFocusable>
                            </div>
                        </div>
                        <div className={styles['nasTV_index_desc_recentlyWatched']}>
                            <div className={styles['nasTV_index_desc_recentlyWatched_list']} ref={scrollContainerRef}>
                                {
                                    movieHistory.map((item: IDescribe, recentlyPlay: number) => {
                                        return (
                                            <MovieCardTV type='play' key={recentlyPlay} getCurrentItem={getCurrentItem} focusableId={`tv-id-recentlyPlay-${item.name}`} focusableRow={2} focusableCol={recentlyPlay} callback={() => setAcKey(recentlyPlay)} cover={item.url} playTime={item.percentage} movieTitle={item.title} time={item.time} />
                                        )
                                    })
                                }
                                <div className={styles.nasTV_index_desc_recentlyWatched_more_item_container}>
                                    <TVFocusable id={`tv-id-recentlyPlay-more-movie`} currentItem={getCurrentItem} row={2} col={movieHistory.length + 1} className={styles.nasTV_index_desc_recentlyWatched_more_item_focus_container} onClick={() => history.push(`${path}/allRecentlyPlay`)}>
                                        <div className={styles.nasTV_index_desc_recentlyWatched_more_item}>查看全部</div>
                                    </TVFocusable>
                                </div>
                                {
                                    hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
                                }
                            </div>
                        </div>
                    </div>
                ) : <></>
            }
        </ErrorComponentTV>
    )
}

export default RecentlyPlay;