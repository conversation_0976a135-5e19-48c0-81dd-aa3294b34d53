.createFolderModal {
  :global(.ant-modal-content) {
    border-radius: 20px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    margin-bottom: 20px;
  }

  :global(.ant-modal-title) {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
  }
}

.inputWrapper {
  margin-bottom: 24px;
  :global {
    :where(.css-dev-only-do-not-override-1d4w9r2).ant-input-affix-wrapper {
      padding: 10px;
    }
  }

  :global(.ant-input) {
    border-radius: 8px;
    font-size: 14px;
    padding: 10px;
  }
}

.clearIcon {
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;

  &:hover {
    color: rgba(0, 0, 0, 0.45);
  }
}

.buttonGroup {
  display: flex;
  justify-content: space-between;
  :global {
    :where(.css-dev-only-do-not-override-1d4w9r2).ant-btn-variant-outlined:not(
        :disabled
      ):not(.ant-btn-disabled):hover,
    :where(.css-dev-only-do-not-override-1d4w9r2).ant-btn-variant-dashed:not(
        :disabled
      ):not(.ant-btn-disabled):hover {
      background-color: rgba(243, 244, 246, 1);
      color: rgba(74, 85, 104, 1);
      border: none;
    }
  }
}

.cancelButton,
.confirmButton {
  flex: 1;
  height: 40px;
  border-radius: 10px;
  font-size: 14px;
  border: none;
}

.cancelButton {
  margin-right: 12px;
  background-color: rgba(243, 244, 246, 1);
  color: rgba(74, 85, 104, 1);
}

.confirmButton {
  margin-left: 12px;
  background-color: rgba(50, 186, 192, 1);
  color: rgba(255, 255, 255, 1);
}
