.nasTV_optPages_container {
  width: 100%;
  height: 100%;
  background: rgba(57, 65, 77, 1);
  color: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  padding: 0 68px 20px 68px;
  user-select: none;
}

.nasTV_optPages_content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 0 40px;
}

.nasTV_index_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
  color: var(--background-color);
}

.nasTV_index_desc {
  width: 100%;
  padding: 150px 75px 60px 75px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.nasTV_index_desc_flag_container {
  display: flex;
  width: 100%;
  gap: 10px;
}

.nasTV_index_desc_flag_add_item {
  padding: 2px 8px;
  color: #fff;
  font-family: MiSans;
  font-size: 30px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
}

.nasTV_index_desc_flag_score,
.nasTV_index_desc_flag_add_container {
  display: flex;
  gap: 10px;
}

.nasTV_index_desc_flag_score {
  .nasTV_index_desc_flag_add_item {
    padding: 2px 0;
    width: 60px;
  }
}

.nasTV_index_desc_flag_score_item {
  display: flex;
  width: 120px;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  border: 2px solid #07b551;

  width: 100%;
  height: 100%;
  color: #07b551;
  font-family: MiSans;
  font-size: 22px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 90.909% */
  letter-spacing: -1px;
}

.nasTV_index_desc_flag_gap {
  line-height: 100%;
  width: 5px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4px;

  div {
    width: 1px;
    height: 12px;
    rotate: 15deg;
    background-color: #fff;
  }
}

.nasTV_index_desc_flag {
  padding: 2px 8px;
  font-size: 24px;
  font-weight: 400;
  border: 1px solid white;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nasTV_index_desc_title {
  height: 133px;
  font-size: 100px;
  font-weight: 400;
}

.nasTV_index_desc_describe {
  width: 727px;
  font-size: 24px;
  font-weight: 400;
}

.nasTV_index_desc_recentlyWatched {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 68px;
  transition: all 0.2s ease-in;
}

.nasTV_index_desc_recentlyWatched_text {
  width: 152px;
  height: 50px;
  font-size: 38px;
  font-weight: 700;
}

.nasTV_index_desc_recentlyWatched_list {
  width: 100%;
  height: 100%;
  display: inline-flex;
  align-items: center;
  gap: 40px;
  user-select: none;
  overflow-x: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;
}

.nasTV_index_desc_recentlyWatched_list_item {
  width: 395px;
  display: flex;
  flex-direction: column;
  padding: 10px 0;

  img {
    width: 385px;
    min-width: 385px;
    height: 220px;
    border-radius: 16px;
  }
}

.nasTV_index_desc_recentlyWatched_list_item_cover {
  width: 395px;
  height: 230px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.nasTV_index_desc_recentlyWatched_list_item_cover_item {
  position: relative;
  height: 220px;
}

.nasTV_index_desc_recentlyWatched_list_item_cover_item_tempImg {
  width: 385px;
  height: 220px;
  border-radius: 16px;
  background-color: rgba(140, 147, 176, 0.1);
}

.nasTV_index_desc_recentlyWatched_list_item_cover:focus {
  border: 4px solid #fff;
  transform: scale(1) !important;
  border-radius: 16px;

  img {
    border-radius: 16px;
  }
}

.nasTV_index_desc_recentlyWatched_list_item_titleSpan,
.nasTV_index_desc_recentlyWatched_list_item_continueTitle {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 22px;
  line-height: 100%;
  letter-spacing: 0px;
  padding: 2px 10px;
  margin-top: 10px;
}

.nasTV_index_desc_recentlyWatched_list_item_titleSpan {
  color: var(--background-color);
}

.nasTV_index_desc_recentlyWatched_list_item_continueTitle {
  opacity: 50%;
  color: var(--card-hover-color);
}

.nasTV_index_desc_recentlyWatched_list_item_progress {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  border-radius: 16px;

  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 96%,
    rgba(255, 255, 255, 0.2)97%,
    rgba(255, 255, 255, 0.2) 100%
  );
}

.nasTV_index_desc_recentlyWatched_list_item_progressBar {
  content: '';
  display: block;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 96%,
    rgba(89, 156, 250, 1)97%,
    rgba(89, 156, 250, 1) 100%
  );
  transition: all 0.3s;
  border-radius: 16px;
}

.nasTV_index_desc_btns {
  display: flex;
  gap: 20px;
  padding: 10px 0;

  .nasTV_index_btns_play_item {
    width: 270px;
    height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--primary-color);
    gap: 15px;
    border-radius: 12px;

    color: #fff;
    text-align: center;
    font-family: MiSans;
    font-size: 33px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    img {
      height: 39px;
    }
  }

  .nasTV_index_btns_item {
    width: 140px;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.2);

    img {
      height: 50px;
    }
  }
}

.nasTV_index_desc_recentlyWatched_more_item_container {
  width: 240px;
  height: 310px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 10px;
}

.nasTV_index_desc_recentlyWatched_more_item_focus_container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 220px;
}

.nasTV_index_desc_recentlyWatched_more_item {
  width: 240px;
  height: 220px;
  background-color: var(--list-value-text-color-reverse);
  font-family: MiSans W;
  font-weight: 400;
  font-size: 22px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.nasTV_index_desc_recentlyWatched_more_item_focus_container:focus {
  border: 4px solid #fff;
  transform: scale(1) !important;
  border-radius: 16px;

  .nasTV_index_desc_recentlyWatched_more_item {
    border-radius: 8px;
  }
}
