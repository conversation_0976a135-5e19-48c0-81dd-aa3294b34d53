.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.videoGroup {
  margin-bottom: 24px;
}

.groupHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: #666;
}

.groupTitle {
  font-weight: 500;
  color: #333;
}

.groupCount {
  margin-left: 8px;
  color: #999;
}

.videoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.videoItem {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.thumbnailContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
}

.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #d0d0d0 0%, #e8e8e8 50%, #d0d0d0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.playIcon {
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

.duration {
  position: absolute;
  bottom: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.videoInfo {
  padding: 8px 12px 12px;
}

.filename {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.datetime {
  flex: 1;
}

.size {
  flex-shrink: 0;
}

.settingsIcon {
  padding: 8px;
  cursor: pointer;
}

.settingsButton {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.settingsDot {
  width: 4px;
  height: 4px;
  background-color: #666;
  border-radius: 50%;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }

  .videoItem {
    background: #2a2a2a;
  }

  .groupTitle {
    color: #fff;
  }

  .filename {
    color: #fff;
  }

  .settingsDot {
    background-color: #fff;
  }
}