.container {
  height: calc(100vh - 35px);
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  padding: 0 16px;
}
.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.content {
  flex: 1;
  padding: 12px 16px;
  overflow-y: auto;
}

.videoGroup {
  margin-bottom: 20px;
}

.groupHeader {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: #666;
}

.groupTitle {
  font-weight: 400;
  color: #999;
  line-height: 20px;
}

.groupCount {
  margin-left: 6px;
  color: #999;
  font-weight: 400;
  line-height: 20px;
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.videoItem {
  background: white;
  border-radius: 6px;
  overflow: hidden;
//   box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.thumbnailContainer {
  position: relative;
  width: 100%;
//   aspect-ratio: 4/3;
aspect-ratio: 1
}

.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
//   background: linear-gradient(135deg, #d0d0d0 0%, #e8e8e8 50%, #d0d0d0 100%);
  display: flex;
  align-items: end;
  justify-content: start;
  position: relative;
}

.playIcon {
  opacity: 0.6;
  transition: opacity 0.2s ease;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    opacity: 0.8;
  }
}

.duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.2;
}

.videoInfo {
  margin-top: 10px;
}

.filename {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 3px;
  display: -webkit-box;
  display: -moz-box; /* 部分旧版 Firefox 需要 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical; /* Firefox 兼容 */
  -webkit-line-clamp: 2; /* WebKit/Blink 核心 */
  line-clamp: 2; /* 标准属性 */
  box-orient: vertical; /* 备用标准属性 (部分场景) */
}

.metadata {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-size: 11px;
  color: #999;
  line-height: 1.2;
}

.datetime {
  font-size: 11px;
}

.size {
  font-size: 11px;
}

.settingsIcon {
  padding: 8px;
  cursor: pointer;
}

.settingsButton {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.settingsDot {
  width: 4px;
  height: 4px;
  background-color: #666;
  border-radius: 50%;
}
