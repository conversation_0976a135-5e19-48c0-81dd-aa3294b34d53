import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import TVFocusable, { FocusableElement } from '../TVFocus';
import { PreloadImage } from '@/components/Image';
import playIcon from '@/Resources/player/play.png';
import collect_icon_tv from '@/Resources/filmWall/collect_icon_tv.png';
import collected_icon_tv from '@/Resources/filmWall/collected_icon_tv.png';
import notWatch_icon_tv from '@/Resources/filmWall/notWatch_icon_tv.png';
import watched_icon_tv from '@/Resources/filmWall/watched_icon_tv.png';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { IDescribe, MovieCardTV } from '../optPages/RecentlyPlay';
import styles from '../optPages/index.module.scss';
import ErrorComponentTV from '../Error';
import { useLibraryListTV } from '..';
import { defaultPageParam } from '../../FATWall_APP/Recently';
import { collect, filmAndTvProps, getRecentlyAdd, markWatched, getMediaFiles, MediaFileResponse } from '@/api/fatWall';
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { Toast } from '@/components/Toast/manager';
import { formatTimeAgo } from '../../FATWall_APP/Recently/RecentlyPlay';
import { playVideo } from '@/api/fatWallPlayer';

interface IRecentlyAddProps {
  score: string;
}

const RecentlyAdd = () => {
  const [acKey, setAcKey] = useState<number>(0);
  const [moveAcKey, setMoveAcKey] = useState<string>('');
  const scrollContainerRef = useRef<HTMLDivElement>(null); // 滚动容器引用
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParam);
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam);
  const [isError, setIsError] = useState<boolean>(false);
  const [addRs, setAddRs] = useState<filmAndTvProps>({ medias: [], count: 0 });
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

  const loaderRef = useRef<HTMLDivElement>(null);

  const movieHistory: (Omit<IDescribe, 'movieDuration'> & IRecentlyAddProps)[] = useMemo(() => {
    return addRs.medias.map((media) => {
      const time = formatTimeAgo(media.create_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : '' : ''; // 数组0索引为封面图，1索引为海报
      return {
        ...media,
        name: media.trans_name, title: media.trans_name, flag: [`${media.year}`, `${media.kind.join(' ')}`, `${media.origin_place}`], score: media.score?.toString() || '暂无评分',
        percentage: `${media.last_seen_percent}`, favourite: media.favourite, seen: media.seen, time: time, describe: media.brief, backgroundImage: poster, url: poster
      }
    })
  }, [addRs.medias])

  const history = useHistory();
  const { path } = useRouteMatch();

  const libs = useLibraryListTV();

  const { runAsync } = useRequest(getRecentlyAdd, { manual: true }); // 获取最近添加的电影列表数据

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('TV端RecentlyAdd获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('TV端RecentlyAdd获取媒体文件列表失败:', error);
        Toast.show('获取剧集列表失败');
      },
    }
  );

  const initData = useCallback(async (callback: (v: filmAndTvProps) => void) => {
    const res = await runAsync(pageOptRef.current).catch(e => console.log(`获取最近观看的电影列表数据失败: ${e}`));
    if (res && res.code === 0 && res.data) {
      if (res.data.count < pageOptRef.current.limit) setHasMore(false);
      callback(res.data);
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [runAsync])

  // 初始化数据加载逻辑
  useEffect(() => {
    initData((v) => setAddRs(v));
  }, [initData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  // 翻页逻辑
  useUpdateEffect(() => {
    pageOptRef.current = pageOpt;
    initData((v) => setAddRs(p => ({ ...p, files: [...p.medias, ...v.medias], count: p.count + v.count })));
  }, [pageOpt, initData])

  // 重置
  const clearAndRefresh = useCallback(() => {
    setHasMore(true);
    setAddRs({ medias: [], count: 0 });

    // 重置分页参数，重新加载数据
    setPageOpt(defaultPageParam);
    if (pageOptRef.current) {
      const { limit, offset } = pageOptRef.current;
      if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
        initData((v) => setAddRs(v));
      }
    }
    pageOptRef.current = defaultPageParam;
  }, [initData])

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建TV端videoList数组，参考APP端逻辑
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file.audio_index || 0, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path || '', // 字幕路径
      subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
    }));

    // 对于RecentlyAdd，默认播放第一集
    let playIndex = 0;
    console.log(`TV端RecentlyAdd播放：将播放第一集，索引位置：${playIndex}`);

    // 调用TV端视频播放接口
    playVideo(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show('开始播放');
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 处理播放按钮点击
  const handlePlayClick = useCallback(() => {
    const currentItem = movieHistory[acKey];
    if (!currentItem) {
      Toast.show('当前没有选中的项目');
      return;
    }

    console.log('TV端RecentlyAdd点击播放:', currentItem);
    
    if (!currentItem.media_id) {
      Toast.show('缺少媒体ID，无法播放');
      return;
    }

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: currentItem.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(currentItem);
  }, [movieHistory, acKey, runGetMediaFiles]);

  const getCurrentItem = useCallback((item: FocusableElement, e) => {
    setMoveAcKey(item.id);
  }, [])

  useUpdateEffect(() => {
    if (!scrollContainerRef.current || movieHistory.length === 0) return; // 确保引用已经初始化
    const scCont = scrollContainerRef.current;
    if (moveAcKey === `tv-id-recentlyAdd-${movieHistory[0].name}`) {
      // scCont.scrollLeft = 0; // 将滚动条位置设置为最左侧

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: 0,
          behavior: 'smooth'
        });
      });
    }
    if (moveAcKey === `tv-id-recentlyAdd-more-movie`) {
      // scCont.scrollLeft = scCont.scrollWidth - scCont.clientWidth; // 将滚动条位置设置为最右侧

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: scCont.scrollWidth - scCont.clientWidth,
          behavior: 'smooth'
        });
      });
    }
  }, [moveAcKey, movieHistory, movieHistory.length])

  const playedByMovie = useCallback(async (seen: number) => {
    if (movieHistory.length > 0 && movieHistory[acKey]) {
      const res = await markWatched({ media_ids: [movieHistory[acKey].media_id], seen: seen });


      if (res && res.code === 0) {
        Toast.show(`${seen ? '已标记' : '取消标记'}观看`);
        clearAndRefresh();
      }
    }
  }, [acKey, clearAndRefresh, movieHistory])

  const likeByMovie = useCallback(async (favourite: number) => {
    if (movieHistory.length > 0 && movieHistory[acKey]) {
      const res = await collect({ media_ids: [movieHistory[acKey].media_id], favourite: favourite });

      if (res && res.code === 0) {
        Toast.show(`${favourite ? '已标记' : '取消标记'}喜欢`);
        clearAndRefresh();
      }
    }
  }, [acKey, clearAndRefresh, movieHistory])

  return (
    <ErrorComponentTV isError={isError} hasContent={movieHistory.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
      text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
      {
        movieHistory[acKey] ? (
          <div className={styles['nasTV_index_container']} style={{ backgroundImage: `url(${movieHistory[acKey].backgroundImage})`, backgroundRepeat: 'no-repeat', backgroundSize: "cover", backgroundPosition: "center" }}>
            <div className={styles['nasTV_index_desc']}>
              <div className={styles['nasTV_index_desc_title']}>
                {movieHistory[acKey].title}
              </div>

              {/* flag */}
              <div className={styles.nasTV_index_desc_flag_container}>
                <div className={styles.nasTV_index_desc_flag_score}>
                  <div className={styles.nasTV_index_desc_flag_add_item}>
                    <div className={styles.nasTV_index_desc_flag_score_item}>{Number(movieHistory[acKey].score).toFixed(1)}</div>
                  </div>
                  <div className={styles.nasTV_index_desc_flag_gap}><div /></div>
                </div>
                {
                  movieHistory[acKey].flag.map((it, index) => (
                    <div key={it} className={styles.nasTV_index_desc_flag_add_container}>
                      <div className={styles.nasTV_index_desc_flag_add_item}>
                        {it}
                      </div>
                      {index !== movieHistory[acKey].flag.length - 1 && <div className={styles.nasTV_index_desc_flag_gap}><div /></div>}
                    </div>
                  ))
                }
              </div>

              <div className={styles['nasTV_index_desc_describe']}>
                {movieHistory[acKey].describe}
              </div>

              {/* 播放、标记、已收藏 */}
              <div className={styles.nasTV_index_desc_btns}>
                <TVFocusable id={`tv-id-recentlyPlay-play`} row={1} col={1} className={styles.nasTV_index_btns_play_item} onClick={handlePlayClick}>
                  <PreloadImage src={playIcon} alt='play' />
                  播放
                </TVFocusable>
                <TVFocusable id={`tv-id-recentlyPlay-like`} row={1} col={2} className={styles.nasTV_index_btns_item} onClick={() => likeByMovie(movieHistory[acKey].favourite ? 0 : 1)}>
                  <PreloadImage src={movieHistory[acKey].favourite ? collected_icon_tv : collect_icon_tv} alt='like' />
                </TVFocusable>
                <TVFocusable id={`tv-id-recentlyPlay-played`} row={1} col={3} className={styles.nasTV_index_btns_item} onClick={() => playedByMovie(movieHistory[acKey].seen ? 0 : 1)}>
                  <PreloadImage src={movieHistory[acKey].seen ? watched_icon_tv : notWatch_icon_tv} alt='played' />
                </TVFocusable>
              </div>
            </div>

            {/* 下方影视展示栏 */}

            <div className={styles['nasTV_index_desc_recentlyWatched']}>
              {/* <span className={styles['nasTV_index_desc_recentlyWatched_text']}>最近观看</span> */}
              <div className={styles['nasTV_index_desc_recentlyWatched_list']} ref={scrollContainerRef}>
                {
                  movieHistory.map((item, index) => {
                    return (
                      <MovieCardTV key={index} type='add' getCurrentItem={getCurrentItem} focusableId={`tv-id-recentlyAdd-${item.name}`} focusableRow={2} focusableCol={index} callback={() => setAcKey(index)} cover={item.url} playTime={item.percentage} movieTitle={item.title} time={item.time} />
                    )
                  })
                }
                <div className={styles.nasTV_index_desc_recentlyWatched_more_item_container}>
                  <TVFocusable id={`tv-id-recentlyAdd-more-movie`} currentItem={getCurrentItem} row={2} col={movieHistory.length + 1} className={styles.nasTV_index_desc_recentlyWatched_more_item_focus_container} onClick={() => history.push(`${path}/allRecentlyAdd`)}>
                    <div className={styles.nasTV_index_desc_recentlyWatched_more_item}>查看全部</div>
                  </TVFocusable>
                </div>
                {
                  hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
                }
              </div>
            </div>

          </div>
        ) : <></>
      }
    </ErrorComponentTV>
  )
}

export default RecentlyAdd;