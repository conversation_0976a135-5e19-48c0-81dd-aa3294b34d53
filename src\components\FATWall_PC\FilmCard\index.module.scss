.container {
  .title {
    width: 260px;
    font-family: MI Lan Pro;
    font-weight: 400;
    font-size: 30px;
    line-height: 36px;
    letter-spacing: 0px;
    text-align: center;
    color: rgba(250, 250, 250, 1);

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .img_container {
    position: relative;
    width: 260px;
    height: 380px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .img_content {
    position: relative;
    width: 240px;
    height: 360px;

    img {
      width: 240px;
      height: 360px;
      border-radius: 16px;
    }
  }

  .score {
    width: 54px;
    height: 34px;
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: rgba(255, 156, 0, 1);
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;

    font-family: MiSans;
    font-weight: 400;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0px;
  }

  .favourite {
    position: absolute;
    bottom: 16px;
    left: 20px;

    img {
      width: 36px;
      height: 36px;
    }
  }
}
