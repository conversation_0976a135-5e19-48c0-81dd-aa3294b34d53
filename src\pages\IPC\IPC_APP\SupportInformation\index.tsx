import { List } from "antd-mobile";
import { useState } from "react";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import supportTest from "@/Resources/camMgmtImg/support-test.png";
import supportTest1 from "@/Resources/camMgmtImg/support-test1.png";
import supportTest2 from "@/Resources/camMgmtImg/support-test2.png";
import supportTest3 from "@/Resources/camMgmtImg/support-test3.png";
import supportTest4 from "@/Resources/camMgmtImg/support-test4.png";
import supportTest5 from "@/Resources/camMgmtImg/support-test5.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { getSupportCameraList, SupportedCameraModel } from "@/api/ipc";
import { PreloadImage } from "@/components/Image";

// 默认摄像机数据
export const defaultCameraList = [
  {
    brand: "小米",
    models: [
      {
        id: 1,
        icon: supportTest,
        name: "小米智能摄像机3 云台版",
      },
      {
        id: 2,
        icon: supportTest1,
        name: "小米室外摄像机CW500双摄版",
      },
      {
        id: 3,
        icon: supportTest2,
        name: "小米智能摄像机 母婴看护版",
      },
      {
        id: 4,
        icon: supportTest3,
        name: "小米智能摄像机 视频通话版",
      },
      {
        id: 5,
        icon: supportTest4,
        name: "小米智能摄像机3 Pro 云台版",
      },
      {
        id: 6,
        icon: supportTest5,
        name: "小米室外摄像机BW500",
      },
    ],
  },
];

// 为了兼容现有代码，保留原来的导出
export const cameraList = defaultCameraList;

const formatApiData = (apiData: SupportedCameraModel[]) => {
  // 获取完整的URL前缀
  const origin = window.location.origin;
  const pathname = window.location.pathname;
  const needPath = pathname.split('/').slice(2, 5).join('/');
  
  return [
    {
      brand: "小米",
      models: apiData.map((camera, index) => ({
        id: index + 1,
        // 拼接完整的图片URL
        icon: `${origin}/${needPath}/${camera.icon}`,
        name: camera.model_name,
        model: camera.model,
      })),
    },
  ];
};

const SupportedCameras = () => {
  const { isDarkMode } = useTheme();
  const [displayCameraList, setDisplayCameraList] = useState(defaultCameraList);

  useRequest(getSupportCameraList, {
    onSuccess: (res) => {
      if (res && res.code === 0) {
        const formattedData = formatApiData(res.data.camera);
        setDisplayCameraList(formattedData);
      } else {
        console.log("接口返回数据为空或失败");
      }
    },
    onError: (error) => {
      console.error("获取支持的摄像机列表失败:", error);
      setDisplayCameraList(defaultCameraList);
    },
  });

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>支持的摄像机</div>
      <div className={styles.content}>
        {displayCameraList.map((brand) => (
          <div key={brand.brand} className={styles.brandSection}>
            <div className={styles.brandName}>{brand.brand}</div>
            <List className={styles.cameraList}>
              {brand.models.map((model) => (
                <List.Item
                  key={model.id}
                  className={styles.cameraItem}
                  arrow={false}
                >
                  <div className={styles.itemContent}>
                    <PreloadImage
                      src={model.icon}
                      alt={model.name}
                      className={styles.cameraIcon}
                    />
                    <span className={styles.cameraName}>{model.name}</span>
                  </div>
                </List.Item>
              ))}
            </List>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SupportedCameras;
