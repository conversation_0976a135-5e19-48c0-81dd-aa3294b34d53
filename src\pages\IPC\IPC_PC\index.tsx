import { createContext, useCallback, useContext, useEffect, useState } from "react";
import styles from "./index.module.scss";
import { Route, Switch, useRouteMatch } from "react-router-dom";
import IPCDesktopEventLookBack from "./EventLookBack";
import FaceManagement from "./FaceRecognition/FaceManagement";
import { ICameraDetail } from "../IPC_APP/CameraDetail";
import InitCamera, { ICamera } from "./InitCamera";
import useIPCSideBar, { ICameraInfoList } from "@/layouts/sideBarHooks/ipc";
import { ICollapsePanel, useSideBarChange } from "@/layouts/Layout";
import { Collapse } from "antd";
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";

import arrow from "@/Resources/layout/arrow.png";
import arrow_dark from "@/Resources/layout/arrow_dark.png";

// 已录制摄像机放入上下文
const recordedCamerasContext = createContext<{
  cameras: (ICollapsePanel & ICameraDetail)[],
  setCameras: (v: (ICollapsePanel & ICameraDetail)[]) => void
}>({ cameras: [], setCameras: (v) => null });

const cameraModelContext = createContext<ICameraInfoList[]>([]); // 摄像机机型
const cameraModalOpenContext = createContext<{ open: (did: string) => void }>({ open: () => null });

export const useCameras = () => useContext(recordedCamerasContext);
export const useOpenCameraModal = () => useContext(cameraModalOpenContext);


// 获取摄像机机型
export const useCameraModel = (model?: string) => {

  const list = useContext(cameraModelContext);
  const [modelInfo, setModelInfo] = useState<ICameraInfoList>();

  useEffect(() => {
    if (list && list.length > 0) {
      const item = list.find((it) => it.model === model);
      if (item) {
        setModelInfo(item);
      }
    }
  }, [list, model])

  return {
    modelInfo,
    list
  }
}

const IPCDesktop = () => {
  const [cameraList, setCameraList] = useState<(ICameraDetail & ICamera)[]>([]);
  const { path } = useRouteMatch();
  const { modalComponent, collapsePanel, setCameras, cameras, cameraInfoList, showDeviceDetail } = useIPCSideBar(); // 折叠面板

  const { setSidePanel } = useSideBarChange(); // 侧边栏变化

  // 缓存打开弹窗方法
  const open = useCallback((did) => {
    showDeviceDetail({ key: did });
  }, [showDeviceDetail]);


  const { isDarkMode } = useTheme();


  // 初始化侧边栏
  useEffect(() => {
    setSidePanel(
      <Collapse bordered={false} expandIconPosition="end" expandIcon={({ isActive }) =>
        (<PreloadImage style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(180deg)' }} src={isDarkMode ? arrow_dark : arrow} alt="collapsed" />)
      } items={collapsePanel} defaultActiveKey={'1'} />
    );
  }, [collapsePanel, isDarkMode, setSidePanel])

  return (
    <>
      <cameraModelContext.Provider value={cameraInfoList}>
        <recordedCamerasContext.Provider value={{ cameras, setCameras }}>
          <cameraModalOpenContext.Provider value={{ open }}>
            <div className={styles.container}>
              <Switch>
                {/* 子路由出口 */}
                <Route exact path={`${path}/eventLookBack`}>
                  <IPCDesktopEventLookBack />
                </Route>
                <Route exact path={`${path}/faceRecognition`}>
                  <FaceManagement />
                </Route>
                {/* 默认内容 */}
                <Route path={path}><InitCamera cameras={cameraList} setCameras={setCameraList} /></Route>
              </Switch>
            </div>

          </cameraModalOpenContext.Provider>
        </recordedCamerasContext.Provider>
      </cameraModelContext.Provider>


      {/* 弹窗显示位置 */}
      <recordedCamerasContext.Provider value={{ cameras, setCameras }}>
        {modalComponent}
      </recordedCamerasContext.Provider>
    </>
  );
};

export default IPCDesktop;
