.container {
  padding: 10px;
  // height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .backIcon {
    cursor: pointer;
  }

  .iconContainer {
    display: flex;
    align-items: center;
  }

  .infoIcon,
  .addIcon {
    cursor: pointer;
  }
}

.cameraStatusContainer {
  padding: 98px;
  flex: 1;
}

.mainContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.iconContainers {
  margin-bottom: 10px;
}

.iconPlaceholder {
  width: 80px;
  height: 80px;
}

.textContainer {
  color: #000;
}

.message {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 2px;
  margin-top: 24px;
}

.storageInfo {
  font-size: 13px;
  color: #999;
}

.footer {
  padding: 24px;
  .storageButton {
    --border-radius: 16px;
    height: 48px;
    font-size: 16px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    font-weight: 500;

    &:disabled {
      opacity: 0.5;
    }
  }
}
