import React, { FC, useMemo, useState } from "react";
import styles from './index.module.scss';
import TVFocusable from "../TVFocus";
import { PreloadImage } from "@/components/Image";
import { useHistory, useRouteMatch } from "react-router-dom";
import { FAT_TV_PREFIX_PATH, useLibraryListTV } from "..";
import search_icon_tv from '@/Resources/filmWall/search_icon_tv.png';
import search_icon_tv_dark from '@/Resources/filmWall/search_icon_tv_dark.png';

interface IOptions {
  name: string;
  label?: string;
  path: string;
}

interface IOptOptions extends IOptions {
  icon: string;
}


const FilmSideBar: FC = () => {
  const history = useHistory();
  const [currentItem, setCurrentItem] = useState<string>(localStorage.getItem('tv-focus') || 'tv-id-sideBar-search');
  const routeMatch = useRouteMatch();

  const currentPath = useMemo(() => {
    const path = routeMatch.path.split('/');
    if (path.length > 2) {
      return path[2];
    }

    return 'recentlyPlay';
  }, [routeMatch.path])

  //功能页的icon
  const pageIconList: Omit<IOptions, 'src'>[] = useMemo(() => {
    return [
      {
        name: "recentlyPlay",
        src: require("@/Resources/componentTestImage/Frame.png"),
        label: '最近观看',
        path: `${FAT_TV_PREFIX_PATH}`
      },
      {
        name: "recentlyAdd",
        src: require("@/Resources/componentTestImage/down.png"),
        label: '最近添加',
        path: `${FAT_TV_PREFIX_PATH}/recentlyAdd`
      },
      {
        name: "all",
        src: require("@/Resources/componentTestImage/down.png"),
        label: '全部',
        path: `${FAT_TV_PREFIX_PATH}/all`
      },
      {
        name: "collect",
        src: require("@/Resources/componentTestImage/Vector.png"),
        label: '收藏',
        path: `${FAT_TV_PREFIX_PATH}/collect`
      },
      {
        name: "played",
        src: require("@/Resources/componentTestImage/Vector.png"),
        label: '已观看',
        path: `${FAT_TV_PREFIX_PATH}/played`
      },
    ];
  }, []);

  //工具类icon
  const optIconList: IOptOptions[] = useMemo(() => {
    return [
      {
        name: "search",
        label: '搜索',
        icon: currentItem === 'tv-id-sideBar-search' ? search_icon_tv_dark : search_icon_tv,
        path: `${FAT_TV_PREFIX_PATH}/search`
      },
    ];
  }, [currentItem]);

  const libs = useLibraryListTV();

  //媒体库icon
  const libraryIconList: IOptions[] = useMemo(() => {
    const base = [
      {
        name: "library1",
        label: '媒体库管理',
        path: ``
      }
    ]
    return [...base, ...libs.libs.map((item) => {
      return {
        name: item.name,
        label: item.name,
        path: `${FAT_TV_PREFIX_PATH}/library/${item.lib_id}`
      }
    })];
  }, [libs.libs]);

  return (
    <div className={styles["sidebar_container"]}>
      {
        /* 工具栏icon */
        optIconList.map((item: IOptOptions, index: number) => (
          <TVFocusable id={`tv-id-sideBar-${item.name}`} currentItem={(it) => setCurrentItem(it.id)}
            row={index} col={0} key={`optIcon` + index} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`} onClick={() => history.push(item.path)}>
            <PreloadImage key={item.name} src={item.icon} alt={item.name} />
            <span>{item.label}</span>
          </TVFocusable>
        ))
      }

      {/* 下划线 */}
      <div className={styles["sidebar_line"]}>
        <PreloadImage src={require("@/Resources/filmWall/line.png")} alt={"line"} />
      </div>

      <div className={styles.sidebar_item_container}>
        {
          /* 页面标题icon */
          pageIconList.map((item: IOptions, index: number) => (
            <TVFocusable id={`tv-id-sideBar-${item.name}`} currentItem={(it) => setCurrentItem(it.id)}
              row={optIconList.length + index} col={0} key={`pageIcon` + index} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`} onClick={() => history.push(item.path)}>
              <span>{item.label}</span>
            </TVFocusable>
          ))
        }
      </div>

      {/* 下划线 */}
      <div className={styles["sidebar_line"]}>
        <PreloadImage src={require("@/Resources/filmWall/line.png")} alt={"line"} />
      </div>
      <div className={styles.sidebar_item_container}>
        {
          /* 媒体库icon */
          libraryIconList.map((item: IOptions, index: number) => (
            <TVFocusable id={`tv-id-sideBar-${item.name}`} currentItem={(it) => setCurrentItem(it.id)}
              row={optIconList.length + pageIconList.length + index} col={0} key={`pageIcon` + index} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`}
              onClick={() => history.push({ pathname: item.path, state: { data: item } })}>
              <span>{item.label}</span>
            </TVFocusable>
          ))
        }
      </div>
    </div>
  );
}

export default FilmSideBar;
