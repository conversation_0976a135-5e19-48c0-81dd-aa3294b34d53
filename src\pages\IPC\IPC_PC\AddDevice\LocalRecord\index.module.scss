.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--desktop-modal-bg-color);
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);

  .backIcon {
    color: var(--text-color);
    cursor: pointer;
    
  }

  .title {
    font-size: 20px;
    font-weight: 500;
    flex: 1;
    text-align: center;
    margin-right: 20px;
  }
}

.recordingModal {
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
      padding: 20px 0;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      padding: 0;
      margin-bottom: 0;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-body {
      padding: 0;
      min-height: 300px;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-footer {
      padding: 16px 40px;
      border-top: none;
      text-align: center;
      margin-top: auto;
    }

    .ant-list-item {
      padding: 10px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-block-end: none !important;

      div {
        font-size: 17px;
        color: var(--text-color);
        display: flex;
        align-items: center;
        font-weight: 500;
      }

      .ant-list-item-extra {
        color: var(--list-value-text-color);
        font-size: 14px;
        margin-left: 8px;
      }
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  padding: 0 24px;
  color: var(--title-color);

  .backIcon {
    font-size: 20px;
    color: var(--text-color);
    cursor: pointer;
    width: 26px;
    height: 26px;
  }

  .title {
    font-size: 20px;
    font-weight: 500;
    flex: 1;
    text-align: center;
    margin-right: 24px;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.settingsSection {
  padding: 16px 16px 0;

  .settingLabel {
    font-size: 14px;
    color: #8c93b0;
    margin-bottom: 8px;
    padding-left: 16px;
  }

  .settingsList {
    padding: 0 16px;
    // background-color: #fff;

    :global {
      .ant-list-split .ant-list-item:last-child {
        border-bottom: none;
      }
    }
  }
}

.arrowIcon {
  font-size: 12px;
  color: #cccccc;
  margin-left: 8px;
}

.hintText {
  text-align: center;
  font-size: 12px;
  color: var(--list-value-text-color);
  padding: 16px 24px 24px;
  line-height: 1.5;
  margin-top: auto;
}

.nextButton {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  background-color: #4086ff;
  border: none;
  font-size: 16px;
  font-weight: normal;

  &:hover,
  &:focus {
    background-color: #4086ff;
  }
}

.customArrow {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.valueWithArrow {
  display: flex;
  align-items: center;
  color: var(--list-value-text-color);
}
.value {
  font-size: 14px !important;
  color: var(--list-value-text-color) !important;
}
