import styles from './index.module.scss';
import { Dispatch, SetStateAction, useCallback, useRef, useState } from 'react';
import TVFocusable, { FocusableElement } from '@/pages/FATWall/FATWall_TV/TVFocus';
import { filterTypeList } from '@/components/FATWall_APP/FATWALL_CONST';
import { defaultTypeList } from '@/pages/FATWall/FATWall_PC/All';
import { useUpdateEffect } from 'ahooks';

const sort = [
  { key: 'latest-add', title: '最新添加' },
  { key: 'high-score', title: '评分最高' },
]

interface IFilmFilter {
  setFilters: Dispatch<SetStateAction<{ [key: string]: string }>>;
  filters: { [key: string]: string };
  isLibrary?: boolean
}

const FilmFilter = (props: IFilmFilter) => {
  const { filters, setFilters, isLibrary } = props;
  const [moveAcKey, setMoveAcKey] = useState<string>(''); // 移动激活的key
  const scrollContainerRef = useRef<HTMLDivElement>(null); // 滚动容器引用

  // 选择回调
  const selectCallback = useCallback((type: string, key: string) => {
    setFilters((filter: any) => {
      let f = { ...filter };
      f[type] = key;
      return f;
    })
  }, [setFilters])

  const getCurrentItem = useCallback((item: FocusableElement, e) => {
    setMoveAcKey(item.id);
  }, [])

  const filterTypeAllRender = useCallback((item, index) => {
    const tempObj: { [key: string]: { key: string, title: string } } = {
      classes: { key: 'all-classes', title: '全部' },
      resolution: { key: 'all-resolution', title: '全部' },
      hdr: { key: 'all-hdr', title: '全部' },
      region: { key: 'all-region', title: '全部' },
      year: { key: 'all-year', title: '全部' },
      kind: { key: 'all-kind', title: '全部' },
      collect: { key: 'all-collect', title: '全部' },
    }

    if (!isLibrary) delete tempObj['collect'];

    return (
      <div className={styles.scroll_container}>
        <TVFocusable currentItem={getCurrentItem} id={`tv-id-all-${tempObj[item.type].key}`} row={index + 2} col={0} className={`${styles.scroll_item}  ${filters[item.type] === tempObj[item.type].key ? styles.selected : ''}`} onClick={() => selectCallback(item.type, tempObj[item.type].key)}>{tempObj[item.type].title}</TVFocusable>
      </div>
    )
  }, [filters, getCurrentItem, isLibrary, selectCallback])

  useUpdateEffect(() => {
    if (!scrollContainerRef.current) return; // 确保引用已经初始化
    const scCont = scrollContainerRef.current;
    const ids = ['tv-id-all-all-classes', 'tv-id-all-all-resolution', 'tv-id-all-all-hdr', 'tv-id-all-comprehensive-sort',
      'tv-id-all-all-region', 'tv-id-all-all-year', 'tv-id-all-all-kind'];

    if (ids.includes(moveAcKey)) {

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: 0,
          behavior: 'smooth'
        });
      });
    }

  }, [moveAcKey])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.category_container}>
          {
            defaultTypeList.map((it, i) => (
              <TVFocusable id={`tv-id-all-${it.key}`} currentItem={getCurrentItem} row={0} col={i} key={it.key} className={`${styles.tabs_item} ${filters['classes'] === it.key ? styles.selected : ''}`} onClick={() => selectCallback('classes', it.key)}>
                <span>{it.title}</span>
              </TVFocusable>
            ))
          }
        </div>
      </div>
      <div className={styles.content} ref={scrollContainerRef}>
        <div className={styles.scroll_container}>
          {
            sort.map((item, index) => (
              <TVFocusable id={`tv-id-all-${item.key}`} currentItem={getCurrentItem} row={1} col={index} key={item.key} className={`${styles.scroll_item}  ${filters['sort'] === item.key ? styles.selected : ''}`} onClick={() => selectCallback('sort', item.key)}>{item.title}</TVFocusable>
            ))
          }
        </div>
        {
          filterTypeList.filter((item) => isLibrary ? item : item.type !== 'collect').map((item, index) => (
            <div className={styles.filter_content_container} key={item.type}>
              {filterTypeAllRender(item, index)}
              <div className={styles.scroll_container}>
                {
                  item.typeList.map((it, i) => (
                    <TVFocusable id={`tv-id-all-${it.key}`} style={{ width: (it.key === '2025-2020' || it.key === '2019-2010' || it.key === '2009-2000') ? '180px' : '' }} currentItem={getCurrentItem} row={index + 2} col={i + 1} key={it.key} className={`${styles.scroll_item} ${filters[item.type] === it.key ? styles.selected : ''}`} onClick={() => selectCallback(item.type, it.key)}>{it.title}</TVFocusable>
                  ))
                }
              </div>
            </div>
          ))
        }
      </div>
    </div>
  )
}

export default FilmFilter;