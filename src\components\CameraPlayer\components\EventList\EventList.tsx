import EventListCard, { IEventListCard } from "./EventListCard";
import styles from "./index.module.scss";

interface IEventList {
  data: IEventListCard[]
  callback?: (value: IEventListCard) => void
  title?: string
}

const EventList = (props: IEventList) => {
  const { data, callback, title } = props;
  return (
    <div className={styles.eventList_container}>
      <div>
        <span className={styles.eventList_title}>{title}</span>
      </div>
      {
        (data || []).map((item, index: number) => (
          <EventListCard key={item.title + index} callback={callback} {...item} />
        ))
      }
    </div>
  )
}

export default EventList;