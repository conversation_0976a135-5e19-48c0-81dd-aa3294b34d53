import { FC, useState, useEffect, useCallback } from "react";
import { Progress, message, Button } from "antd";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import GuidePanel from "../components/GuidePanel";
import img from "@/Resources/nasDiskImg/automatic-download.png";
import fileIcon from "@/Resources/nasDiskImg/file-icon.png";
import start from "@/Resources/nasDiskImg/start.png";
import delIcon from "@/Resources/nasDiskImg/Union.png";
import pause from "@/Resources/nasDiskImg/startIcon.png";
import SelectFolderModal from "../components/SelectFolderModal";

import { PreloadImage } from "@/components/Image";
import {
  TaskInfo,
  controlTask,
  ControlTaskParams,
  downloadFromBaiduNetdisk,
  BaiduDownloadPathItem,
} from "@/api/nasDisk";
import request from "@/request";
import { modalShow } from "@/components/List";
import { getPoolInfo } from "@/api/fatWall";
import { formatBytes } from "../../NasDisk_APP/SynchronizationTab";

// 最大任务数量限制
const MAX_TASK_COUNT = 5;

// 获取任务显示名称
const getTaskDisplayName = (task: TaskInfo): string => {
  if (task.src && task.src.length > 0) {
    const path = decodeURIComponent(task.src[task.src.length - 1]);
    return path.startsWith("/") ? path.substring(1) : path;
  }
  return task.dst?.split("/").pop() || `任务${task.task_id}`;
};

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  itemCount?: number;
  isLiked?: boolean;
  path: string;
}

const ScheduledDownload: FC = () => {
  // 下载任务列表
  const [downloadingTasks, setDownloadingTasks] = useState<TaskInfo[]>([]);
  const [, setHasError] = useState(false);
  const [, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 文件夹选择相关状态
  const [selectFolderVisible, setSelectFolderVisible] = useState(false);
  // 下载位置相关状态
  const [downloadPath, setDownloadPath] = useState<string>("");
  const [downloadDisplayPath, setDownloadDisplayPath] =
    useState<string>("内部存储01 > 百度网盘");
  // 存储池和WebDAV配置信息
  const [defaultDownloadPath, setDefaultDownloadPath] = useState<string>("");
  const [, setWebDAVConfig] = useState<{ alias_root?: string } | null>(null);

  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {

    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        // 保存WebDAV配置
        if (response.data.webDAV) {
          setWebDAVConfig(response.data.webDAV);
        }

        // 获取第一个存储池的顶层目录作为默认下载路径
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;

            // 设置默认的下载路径
            const defaultPath = `${aliasRoot}${dataDir}/百度网盘`;
            setDefaultDownloadPath(defaultPath);
            setDownloadPath(defaultPath);
          }
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
    },
  });

  // 组件挂载时获取存储池信息
  useEffect(() => {
    fetchPoolInfo({});
  }, [fetchPoolInfo]);

  // 使用useRequest处理任务控制
  const { run: runControlTask } = useRequest(
    (params: ControlTaskParams) => controlTask(params),
    {
      manual: true,
      onSuccess: (result, params) => {
        if (result.code === 0) {
          const commandText: Record<string, string> = {
            pause: "暂停",
            continue: "启动",
            cancel: "取消",
            restart: "重启",
          };
          const command = params[0].command;
          message.success(`任务已${commandText[command] || "操作成功"}`);
          // 重新获取任务列表
          fetchAutoDownloadTasks();
        } else {
          message.error(result.result || "操作失败");
        }
      },
      onError: (error) => {
        console.error("任务操作失败:", error);
        message.error("操作失败，请重试");
      },
    }
  );

  // 添加自动下载请求
  const { run: runAutoDownload } = useRequest(
    (params: {
      remotePath: BaiduDownloadPathItem[];
      localPath: string;
      autotask: number;
    }) => {
      return downloadFromBaiduNetdisk({
        action: "download",
        autotask: params.autotask,
        remotepath: params.remotePath,
        localpath: params.localPath,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 0) {
          message.success("自动下载任务添加成功");
          // 关闭文件夹选择弹窗
          setSelectFolderVisible(false);
          // 重新获取任务列表
          fetchAutoDownloadTasks();
        } else {
          message.error(`自动下载任务添加失败: ${result.result || "未知错误"}`);

          if (result.failed_paths && result.failed_paths.length > 0) {
            console.error("下载失败的路径:", result.failed_paths);
          }
        }
      },
      onError: (error) => {
        console.error("自动下载请求出错:", error);
        message.error("自动下载请求出错，请重试");
      },
    }
  );

  // 获取自动下载任务
  const fetchAutoDownloadTasks = useCallback(async () => {
    // 只有在有任务时才显示加载状态，避免空状态时的闪动
    if (downloadingTasks.length > 0) {
      setIsLoading(true);
    }

    try {
      // 获取所有自动下载任务
      const response = await request.post(
        "/taskcenter/get_taskinfo",
        {
          selector: [
            {
              key: "module",
              value: ["bpan"],
            },
            {
              key: "type",
              value: ["active"],
            },
            {
              key: "action",
              value: ["auto_download"],
            },
          ],
        },
        { showLoading: false }
      ); // 禁用loading动画

      // 处理任务
      if (response?.data?.info) {
        const tasks = response.data.info;
        // 按状态排序：running > paused > waiting > success_waiting > 其他
        const sortedTasks = tasks.sort((a: TaskInfo, b: TaskInfo) => {
          const statusOrder: Record<string, number> = {
            running: 0,
            paused: 1,
            waiting: 2,
            success_waiting: 3,
            failed: 4,
            cancelled: 5,
            "partial error": 6,
          };

          const orderA = statusOrder[a.status] ?? 999;
          const orderB = statusOrder[b.status] ?? 999;

          return orderA - orderB;
        });

        setDownloadingTasks(sortedTasks);
      } else {
        setDownloadingTasks([]);
      }

      setHasError(false);
    } catch (error) {
      console.error("获取自动下载任务失败:", error);
      setHasError(true);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [downloadingTasks.length]);

  // 初始化数据
  useEffect(() => {
    // 立即获取数据
    fetchAutoDownloadTasks();
  }, [fetchAutoDownloadTasks]);

  // 根据任务数量设置轮询
  useEffect(() => {
    // 只有当有任务时才设置轮询
    let timer: NodeJS.Timeout | null = null;

    if (downloadingTasks.length > 0) {
      timer = setInterval(fetchAutoDownloadTasks, 5000); // 每5秒轮询任务
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [downloadingTasks.length, fetchAutoDownloadTasks]);

  // 导航到选择文件夹页面
  const handleSelectFolder = () => {
    // 检查是否已达到最大任务数量
    if (downloadingTasks.length >= MAX_TASK_COUNT) {
      message.warning(
        `最多只能添加${MAX_TASK_COUNT}个自动下载任务，请删除后再添加`
      );
      return;
    }

    // 打开文件夹选择弹窗
    setSelectFolderVisible(true);
  };

  // 处理文件夹选择
  const handleFolderSelect = async (
    folders: FileItem[],
    selectedPath?: string,
    selectedDisplayPath?: string
  ) => {
    try {
      if (folders.length === 0) {
        message.error("请至少选择一个文件夹");
        return;
      }

      // 使用选择的下载路径或默认路径
      const targetPath = selectedPath || downloadPath || defaultDownloadPath;

      // 遍历选中的文件夹，为每个文件夹创建自动下载任务
      for (const folder of folders) {
        // 准备下载参数
        const remotePath: BaiduDownloadPathItem[] = [
          {
            type: "directory",
            path: folder.path,
          },
        ];

        // 调用自动下载接口
        runAutoDownload({
          remotePath,
          localPath: targetPath,
          autotask: 1, // 自动任务
        });
      }

      // 如果有新的下载路径，更新状态
      if (selectedPath && selectedDisplayPath) {
        setDownloadPath(selectedPath);
        setDownloadDisplayPath(selectedDisplayPath);
      }
    } catch (error) {
      console.error("创建自动下载任务失败:", error);
      message.error("创建任务失败，请重试");
    }
  };

  // 暂停/启动任务
  const handleTogglePause = (
    taskId: string,
    currentStatus: string,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    try {
      const command =
        currentStatus === "running" || currentStatus === "waiting"
          ? "pause"
          : "continue";
      runControlTask({
        task_id: [taskId],
        command: command,
      });
    } catch (error) {
      console.error("操作任务失败:", error);
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    modalShow(
      "删除自动下载任务",
      <div className={styles.deleteConfirmContent}>
        仅删除自动下载任务，不会删除已下载文件
      </div>,
      (modal) => {
        // 使用控制任务接口取消任务
        runControlTask({
          task_id: [taskId],
          command: "cancel",
        });
        // 立即更新本地状态，提升用户体验
        setDownloadingTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.task_id === taskId ? { ...task, status: "cancelled" } : task
          )
        );
        modal.destroy();
      },
      () => {
        // 取消删除
        console.log("取消删除任务");
      },
      false,
      {
        position: "center",
        okBtnText: "确定",
        cancelBtnText: "取消",
        contentHeight: "auto",
        okBtnStyle: { backgroundColor: "#32BAC0", color: "#fff" },
      }
    );
  };

  // 获取任务状态文本
  const getTaskStatusText = (task: TaskInfo) => {
    switch (task.status) {
      case "waiting":
        return "等待中";
      case "running":
        return (
          `${formatBytes(task.detail?.handle_size || 0)}/${formatBytes(
            task.detail?.total_size || 0
          )}` || "下载中"
        );
      case "paused":
        return "已暂停";
      case "success_waiting":
        return "已完成";
      case "failed":
        return "下载失败";
      case "cancelled":
        return "已取消";
      case "partial error":
        return "部分失败";
      default:
        return "未知状态";
    }
  };

  // 获取任务路径显示文本
  const getTaskPathDisplay = (task: TaskInfo) => {
    if (!task.src || task.src.length === 0) return "内部存储01";

    // 处理路径，移除前导斜杠
    const cleanPaths = task.src.map((path) =>{
      const decodePath = decodeURIComponent(path)
      return decodePath.startsWith("/") ? decodePath.substring(1) : decodePath
    }
    );

    // 只显示前两级路径
    if (cleanPaths.length <= 2) {
      return cleanPaths.join(" > ");
    }

    // 如果路径超过两级，显示第一级和最后一级
    const firstPath = cleanPaths[0];
    const lastPath = cleanPaths[cleanPaths.length - 1];

    if (cleanPaths.length === 3) {
      return `${firstPath} > ${cleanPaths[1]} > ${lastPath}`;
    }

    return `${firstPath} > ${lastPath}`;
  };

  // 计算任务的更新时间显示
  const getTaskUpdateTime = (task: TaskInfo) => {
    const timestamp = task.status === "success_waiting" ? task.detail?.finish_time : task.create_time;
    if (!timestamp) return "";

    const taskTime = parseInt(timestamp);
    const now = Date.now();
    const diffMinutes = Math.floor((now - taskTime) / (60 * 1000));

    if (diffMinutes < 1) return "刚刚更新";
    if (diffMinutes < 60) return `${diffMinutes}分钟前更新`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}小时前更新`;

    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}天前更新`;

    // 格式化日期
    const date = new Date(taskTime);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 获取任务完成比例文本
  const getTaskCompletionRatio = (task: TaskInfo) => {
    if (!task.detail) return "";
    if (task.status === "success_waiting") return "";
    if (
      task.status === "running" ||
      task.status === "paused" ||
      task.status === "waiting"
    ) {
      const { finish_file_cnt = 0, total_file_cnt } = task.detail;
      return `${finish_file_cnt}/${total_file_cnt}`;
    }
    return "";
  };

  // 空状态UI
  const renderEmptyState = () => (
    <GuidePanel
      imageSrc={img}
      imageAlt="百度网盘自动下载至智能存储"
      title="百度网盘自动下载至智能存储"
      description="百度网盘的文件夹设置为自动下载后，智能存储会定期（非休眠期间每小时）检查此文件夹是否有新文件加入，如果有新文件，将会自动把新增文件下载到智能存储"
      buttonText="新增自动下载任务"
      onButtonClick={handleSelectFolder}
    />
  );

  // 任务列表UI
  const renderTaskList = () => (
    <div className={styles.taskContainer}>
      <div className={styles.taskHeader}>
        <div className={styles.headerTitle}>自动下载的文件夹</div>
        <div className={styles.headerActions}>
          <Button
            color="default"
            className={styles.addButton}
            disabled={downloadingTasks.length >= MAX_TASK_COUNT}
            onClick={handleSelectFolder}
          >
            新增
          </Button>
        </div>
      </div>

      <div className={styles.taskList}>
        {downloadingTasks.map((task) => (
          <div key={task.task_id} className={styles.taskItem}>
            <div className={styles.taskLeftSection}>
              <div className={styles.taskIcon}>
                <PreloadImage
                  src={fileIcon}
                  alt=""
                  className={styles.folderIcon}
                />
              </div>
              <div className={styles.taskName}>{getTaskDisplayName(task)}</div>
            </div>

            <div className={styles.taskMiddleSection}>
              <div className={styles.taskAll}>
                <div className={styles.taskStatus}>
                  {getTaskStatusText(task)}
                </div>
                <div className={styles.progressCounter}>
                  {getTaskCompletionRatio(task)}
                </div>
              </div>
              <div className={styles.progressWrapper}>
                <Progress
                  percent={task.detail?.progress || 0}
                  showInfo={false}
                  strokeColor="#32BAC0"
                  className={styles.progressBar}
                />
              </div>
              <div className={styles.pathBox}>
                <div className={styles.taskPath}>
                  {getTaskPathDisplay(task)}
                </div>

                <div className={styles.updateTime}>
                  {getTaskUpdateTime(task)}
                </div>
              </div>
            </div>

            <div className={styles.taskRightSection}>
              <div className={styles.actionButtons}>
                {task.status === "success_waiting" ? (
                  <img
                    src={start}
                    alt="开始"
                    className={styles.controlIcon}
                    onClick={(e) =>
                      handleTogglePause(task.task_id, task.status, e)
                    }
                  />
                ) : task.status !== "paused" ? (
                  <img
                    src={pause}
                    alt="暂停"
                    className={styles.controlIcon}
                    onClick={(e) =>
                      handleTogglePause(task.task_id, task.status, e)
                    }
                  />
                ) : (
                  <img
                    src={start}
                    alt="开始"
                    className={styles.controlIcon}
                    onClick={(e) =>
                      handleTogglePause(task.task_id, task.status, e)
                    }
                  />
                )}
                <img
                  src={delIcon}
                  alt="删除"
                  className={styles.deleteImg}
                  onClick={(e) => handleDeleteTask(task.task_id, e)}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (!isInitialized) {
    return <div className={styles.loading}>加载中...</div>;
  }

  return (
    <div className={styles.scheduledDownload}>
      {downloadingTasks.length > 0 ? renderTaskList() : renderEmptyState()}
      <SelectFolderModal
        visible={selectFolderVisible}
        onClose={() => setSelectFolderVisible(false)}
        onSelect={handleFolderSelect}
        maxTaskCount={MAX_TASK_COUNT - downloadingTasks.length} // 传递剩余可选数量
        downloadPath={downloadPath}
        downloadDisplayPath={downloadDisplayPath}
        existingFolderPaths={downloadingTasks.map(task => task.src || []).flat()} // 传递已有任务的文件夹路径
      />
    </div>
  );
};

export default ScheduledDownload;
