import { FC, useState, useEffect, useMemo, useRef } from "react";
import styles from "./index.module.scss";
import { LeftOutlined, HeartOutlined, CheckOutlined, HeartFilled } from "@ant-design/icons";
import play_white from "@/Resources/player/play_dashboard.png"
import { PreloadImage } from "@/components/Image";
import { message } from "antd";
import { Toast } from '@/components/Toast/manager';
import { useHistory, useLocation } from "react-router-dom";
import EpisodeList, { Episode } from "@/components/FATWall_PC/EpisodeList";

import TVFocusable from "../TVFocus";
import { useRequest } from 'ahooks';
import {
  getMediaDetails,
  getMediaFiles,
  collect,
  markWatched,
  MediaDetailsResponse,
  MediaFileInfo,
  collectEpisode,
  markWatchedEpisode,
} from "@/api/fatWall";
import { playVideo } from "@/api/fatWallPlayer";
import CommonUtils from '@/utils/CommonUtils';


interface VideoDetailsProps {
  videoId?: string;
}

// 扩展Episode接口，添加需要的字段
interface ExtendedEpisode extends Episode {
  path?: string;
  file_id?: number;
  resolution?: string;
  hdr?: string;
  audio_codec?: string;
  total_time?: number;
}

// 格式化文件大小的函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};


const VideoDetails: FC<VideoDetailsProps> = ({ videoId }) => {
  const history = useHistory();
  const location = useLocation();

  // 支持URL参数和state参数两种方式
  const urlParams = new URLSearchParams(location.search);
  const urlClasses = urlParams.get('classes');
  const urlMediaId = urlParams.get('media_id');
  const urlLibId = urlParams.get('lib_id');
  const urlIsDrama = urlParams.get('isDrama');

  // 优先使用URL参数，如果没有则使用state参数
  const stateParams = location.state as { classes: string, media_id: number, lib_id?: number, isDrama?: boolean } || { classes: '', media_id: 0 };
  const classes = urlClasses || stateParams.classes || '';
  const media_id = urlMediaId ? parseInt(urlMediaId) : (stateParams.media_id || 0);
  const lib_id = urlLibId ? parseInt(urlLibId) : (stateParams.lib_id || 0); // 默认为0表示"All"页面
  const isDrama = urlIsDrama === 'true' || stateParams.isDrama || (classes === '电视剧');

  const [isLoading, setIsLoading] = useState(true);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [mediaDetails, setMediaDetails] = useState<MediaDetailsResponse | null>(null);
  const [mediaFiles, setMediaFiles] = useState<MediaFileInfo[]>([]);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [isWatched, setIsWatched] = useState<boolean>(false);
  const [currentEpisodeId, setCurrentEpisodeId] = useState<string>("0");
  const [episodeFavorites, setEpisodeFavorites] = useState<Map<number, boolean>>(new Map());
  const [episodeWatched, setEpisodeWatched] = useState<Map<number, boolean>>(new Map());
  const [currentFavoriteOperation, setCurrentFavoriteOperation] = useState<'collect' | 'uncollect' | null>(null);
  const [currentWatchedOperation, setCurrentWatchedOperation] = useState<'watched' | 'unwatched' | null>(null);
  const [selectedVersionId, setSelectedVersionId] = useState<string>('1'); // 版本选择器状态
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 获取媒体详情
  const { run: runGetMediaDetails } = useRequest(
    getMediaDetails,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaDetails(res.data);
          // 更新收藏和已看状态
          setIsFavorite(res.data.favourite === 1);
          setIsWatched(res.data.seen === 1);
          setIsLoading(false);
        }
      },
      onError: () => {
        message.error('获取影视详情失败');
        setIsLoading(false);
      },
    }
  );

  // 获取媒体文件列表
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaFiles(res.data.files || []);

          // 初始化单集收藏和观看状态
          const favoriteMap = new Map<number, boolean>();
          const watchedMap = new Map<number, boolean>();

          res.data.files.forEach(file => {
            favoriteMap.set(file.file_id, file.favourite === 1);
            watchedMap.set(file.file_id, file.seen === 1);
          });

          setEpisodeFavorites(favoriteMap);
          setEpisodeWatched(watchedMap);
        }
      },
      onError: () => {
        message.error('获取文件列表失败');
      },
    }
  );

  // 收藏/取消收藏
  const { run: runCollect } = useRequest(
    collect,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isFavorite ? '已添加到收藏' : '已取消收藏');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          setIsFavorite(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          setIsFavorite(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        setIsFavorite(prev => !prev);
      },
    }
  );

  // 标记已观看/未观看
  const { run: runMarkWatched } = useRequest(
    markWatched,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isWatched ? '标记为已观看' : '标记为未观看');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          setIsWatched(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          setIsWatched(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        setIsWatched(prev => !prev);
      },
    }
  );

  // 单集收藏/取消收藏
  const { run: runCollectEpisode } = useRequest(
    collectEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          const successMessage = currentFavoriteOperation === 'collect' ? '已添加到收藏' : '已取消收藏';
          message.success(successMessage);
        } else {
          message.error('操作失败，请重试');
          runGetMediaFiles({ lib_id, media_id });
        }
        setCurrentFavoriteOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        runGetMediaFiles({ lib_id, media_id });
        setCurrentFavoriteOperation(null);
      },
    }
  );

  // 单集标记已观看/未观看
  const { run: runMarkWatchedEpisode } = useRequest(
    markWatchedEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          const successMessage = currentWatchedOperation === 'watched' ? '已标记为观看' : '已取消观看';
          message.success(successMessage);
        } else {
          message.error('操作失败，请重试');
          runGetMediaFiles({ lib_id, media_id });
        }
        setCurrentWatchedOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        runGetMediaFiles({ lib_id, media_id });
        setCurrentWatchedOperation(null);
      },
    }
  );

  // 处理焦点元素的滚动
  const handleFocusScroll = (item: any) => {
    if (scrollContainerRef.current && item.ref.current) {
      const container = scrollContainerRef.current;
      const element = item.ref.current;
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      // 检查元素是否在可视区域外
      if (elementRect.bottom > containerRect.bottom || elementRect.top < containerRect.top) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  useEffect(() => {
    // 调用接口获取媒体详情
    runGetMediaDetails({ media_id });
    // 调用接口获取媒体文件列表
    runGetMediaFiles({ lib_id, media_id });
  }, [runGetMediaDetails, runGetMediaFiles, media_id, lib_id]);

  // 设置默认版本选择器值
  useEffect(() => {
    if (mediaFiles && mediaFiles.length > 0) {
      setSelectedVersionId(mediaFiles[0].file_id.toString());
    }
  }, [mediaFiles, classes]);

  const handleBack = () => {
    history.goBack();
  };

  const handlePlay = () => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建完整的videoList数组，与APP端保持一致
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: (file.media_id || media_id).toString(), // 如果file.media_id为空，使用页面的media_id
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file?.audio_index || 0, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path || '', // 字幕路径
      subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
    }));

    let playIndex = 0; // 默认播放第一集

    // 根据classes判断播放逻辑，与APP端保持一致
    if (classes === '电影') {
      // 电影：根据版本选择器确定播放索引
      if (mediaFiles.length >= 2) {
        const selectedFileIndex = mediaFiles.findIndex(file => file.file_id.toString() === selectedVersionId);
        if (selectedFileIndex !== -1) {
          playIndex = selectedFileIndex;
        }
      }
    } else {
      // 剧集类型：根据media_details中的last_seen_file_id在file_list数组中筛选file_id相同的项的索引位置
      if (mediaDetails?.last_seen_file_id) {
        // 遍历mediaFiles数组，找到file_id与last_seen_file_id相同的项的索引
        const targetIndex = mediaFiles.findIndex(file => file.file_id === mediaDetails.last_seen_file_id);
        if (targetIndex !== -1) {
          playIndex = targetIndex;
          console.log(`TV端剧集播放：找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的索引位置：${targetIndex}`);
        } else {
          console.log(`TV端剧集播放：未找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的文件，将播放第一集`);
        }
      } else {
        console.log('TV端剧集播放：没有last_seen_file_id，将播放第一集');
      }
    }

    // 调用视频播放接口
    playVideo(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show('开始播放');
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });
  };



  const handleToggleFavorite = () => {
    const newFavoriteStatus = !isFavorite;
    setIsFavorite(newFavoriteStatus);

    // 调用收藏API
    runCollect({
      media_ids: [media_id],
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleWatched = () => {
    const newWatchedStatus = !isWatched;
    setIsWatched(newWatchedStatus);

    // 调用标记已观看API
    runMarkWatched({
      media_ids: [media_id],
      seen: newWatchedStatus ? 1 : 0
    });
  };



  const handleToggleEpisodeFavorite = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newFavoriteStatus = !episode.favorite;
    const operationType = newFavoriteStatus ? 'collect' : 'uncollect';

    // 保存当前操作类型
    setCurrentFavoriteOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeFavorites(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newFavoriteStatus);
      return newMap;
    });

    // 调用API
    runCollectEpisode({
      file_id: episode.file_id,
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleEpisodeWatched = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newWatchedStatus = !episode.watched;
    const operationType = newWatchedStatus ? 'watched' : 'unwatched';

    // 保存当前操作类型
    setCurrentWatchedOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeWatched(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newWatchedStatus);
      return newMap;
    });

    // 调用API
    runMarkWatchedEpisode({
      file_id: episode.file_id,
      seen: newWatchedStatus ? 1 : 0
    });
  };

  const handleEpisodeSelect = (episode: Episode) => {
    setCurrentEpisodeId(episode.id);

    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建完整的videoList数组，与APP端保持一致
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: (file.media_id || media_id).toString(), // 如果file.media_id为空，使用页面的media_id
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file?.audio_index || 0, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path || '', // 字幕路径
      subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
    }));

    // 根据选中的剧集找到对应的索引
    const selectedEpisodeIndex = mediaFiles.findIndex(file => file.file_id.toString() === episode.id);
    const playIndex = selectedEpisodeIndex !== -1 ? selectedEpisodeIndex : 0;

    // 调用视频播放接口
    playVideo(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show(`开始播放第${episode.episodeNumber}集`);
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });
  };

  // 将媒体文件转换为剧集列表格式
  const episodes: ExtendedEpisode[] = useMemo(() => {
    if (!mediaFiles || mediaFiles.length === 0) {
      return [];
    }

    return mediaFiles.map((file) => {
      const posterUrl = (mediaDetails?.poster && mediaDetails.poster.length > 0 ? mediaDetails.poster[0] : '');

      return {
        id: file.file_id.toString(),
        title: `第${file.episode}集`,
        thumbnail: posterUrl,
        episodeNumber: file.episode,
        watched: episodeWatched.get(file.file_id) ?? false,
        progress: file.last_play_point > 0 ? Math.min(Math.round(file.last_play_point), 100) : 0,
        favorite: episodeFavorites.get(file.file_id) ?? false,
        file_id: file.file_id,
        path: file.path,
        resolution: file.resolution,
        hdr: file.hdr,
        audio_codec: file.audio_codec,
        file_media_id: file?.media_id
      };
    });
  }, [mediaFiles, mediaDetails, episodeFavorites, episodeWatched]);

  // 创建演员列表数据
  const castMembers = useMemo(() => {
    if (!mediaDetails || !mediaDetails.actor_info) return [];

    return mediaDetails.actor_info.map((actor, index) => ({
      id: index.toString(),
      name: actor?.name || '未知演员',
      role: actor.chapter ? `饰 ${actor.chapter}` : '演员',
      avatar: actor.profile_path || ''
    }));
  }, [mediaDetails]);

  // 视频信息，使用真实数据
  const videoData = useMemo(() => {
    if (!mediaDetails) {
      return {
        title: '加载中...',
        rating: 0,
        year: 0,
        category: '加载中',
        region: '',
        duration: '0分钟',
        tags: [],
        description: '加载中...',
        filePath: '',
        fileSize: '0 B',
        cast: [],
        playTime: ''
      };
    }

    // 使用接口返回的真实数据
    return {
      title: mediaDetails.trans_name || mediaDetails.origin_name || '未知影片',
      rating: mediaDetails.score || 0,
      year: mediaDetails.year || 0,
      category: mediaDetails.classes || '未知',
      region: mediaDetails.origin_place || '未知',
      duration: (() => {
        const totalMinutes = mediaDetails.video_length;
        if (!totalMinutes) return '未知时长';

        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;

        if (hours === 0) {
          return `${minutes}分钟`;
        } else if (minutes === 0) {
          return `${hours}小时`;
        } else {
          return `${hours}小时${minutes}分钟`;
        }
      })(),
      tags: mediaFiles && mediaFiles.length > 0 ? [
        mediaFiles[0].resolution,
        mediaFiles[0].hdr,
        mediaFiles[0].audio_codec
      ].filter(Boolean) : [],
      description: mediaDetails.brief || '暂无简介',
      filePath: mediaFiles && mediaFiles.length > 0 ? mediaFiles[0].path : '',
      fileSize: mediaFiles && mediaFiles.length > 0 ? formatFileSize(mediaFiles[0].file_size || 0) : '未知大小',
      cast: castMembers,
      // 获取播放进度信息
      playTime: (() => {
        // 如果是电影类型，显示播放时间
        if (classes === '电影') {
          const lastPlayPoint = mediaDetails?.last_play_point || 0;
          if (lastPlayPoint > 0) {
            const minutes = Math.floor(lastPlayPoint / 60);
            const seconds = lastPlayPoint % 60;
            return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
          }
          return '';
        } else {
          // 如果是剧集类型，显示上次播放的集数
          const lastSeenFileId = mediaDetails?.last_seen_file_id;
          if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
            const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
            if (lastSeenFile && lastSeenFile.episode) {
              return `第${lastSeenFile.episode}集`;
            }
          }
          // 没有播放记录时显示第1集
          return mediaFiles && mediaFiles.length > 0 ? '第1集' : '';
        }
      })()
    };
  }, [mediaDetails, mediaFiles, castMembers, classes]);

  // 加载中状态
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>加载中...</div>
        {/* 返回按钮 - 固定在顶部 */}
        <TVFocusable
          id="tv-focus-videoDetails-back-button"
          row={0}
          col={0}
          onClick={handleBack}
          className={styles.backButton}
          currentItem={handleFocusScroll}
        >
          <LeftOutlined />
        </TVFocusable>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 背景视频/图片 */}
      <div className={styles.videoBackground}>
        {mediaDetails?.poster && mediaDetails.poster.length > 0 && (
          <img src={mediaDetails.poster[0]} alt={videoData.title} />
        )}
      </div>

    
      {/* 可滚动的内容区域 */}
      <div className={`${styles.scrollableContent} ${isDrama ? styles.dramaScrollableContent : ''}`} ref={scrollContainerRef}>
        {/* 视频标题 */}
        <h1 className={styles.title}>{videoData.title}</h1>

        {/* 视频信息行 */}
        <div className={styles.infoRow}>
          <span className={styles.rating}>{Number(videoData.rating).toFixed(1)}</span>
          <span>{videoData.year}|</span>
          <span>{videoData.category}|</span>
          <span>{videoData.region}|</span>
          <span>{videoData.duration}</span>
        </div>

        {/* 视频标签 */}
        <div className={styles.infoRow}>
          {videoData.tags.map((tag, index) => (
            <span key={index} className={styles.tag}>{tag}</span>
          ))}
        </div>

        {/* 视频描述 */}
        <div className={styles.descriptionContainer}>
          <div className={`${styles.description} ${showFullDescription ? styles.expanded : ''}`}>
            {videoData.description}
          </div>
          <span className={styles.toggleButton} onClick={() => setShowFullDescription(!showFullDescription)}>
            {showFullDescription ? "收起" : "更多"}
          </span>
        </div>

        {/* 操作按钮 */}
        <div className={styles.buttons}>
          <TVFocusable
            id="tv-focus-videoDetails-play-button"
            row={0}
            col={0}
            onClick={handlePlay}
            className={styles.primaryButton}
            currentItem={handleFocusScroll}
          >
            <PreloadImage src={play_white} style={{ width: "30px", height: "30px" }} alt="play_white" />
            {classes === '电影' ?
              <div>播放</div>
              :
              <div style={{ display: 'flex' }}>
                <span>播放</span>
                <span>{videoData.playTime}</span>
              </div>
            }
          </TVFocusable>
          
          <TVFocusable
            id="tv-focus-videoDetails-favorite-button"
            row={0}
            col={1}
            onClick={handleToggleFavorite}
            className={styles.secondaryButton}
            currentItem={handleFocusScroll}
          >
            {isFavorite ?
              <HeartFilled style={{ fontSize: "20px", color: "#FF4D4F" }} /> :
              <HeartOutlined style={{ fontSize: "20px", color: "white" }} />
            }
            <div>收藏</div>
          </TVFocusable>
          
          <TVFocusable
            id="tv-focus-videoDetails-watched-button"
            row={0}
            col={2}
            onClick={handleToggleWatched}
            className={styles.secondaryButton}
            currentItem={handleFocusScroll}
          >
            <CheckOutlined style={{ fontSize: "20px", color: isWatched ? "#1890FF" : "white" }} />
            <div>已观看</div>
          </TVFocusable>
        </div>

        {/* 剧集列表 */}
        {isDrama && (
          <EpisodeList
            episodes={episodes}
            isTv={true}
            currentEpisodeId={currentEpisodeId}
            onEpisodeSelect={handleEpisodeSelect}
            onToggleFavorite={handleToggleEpisodeFavorite}
            onToggleWatched={handleToggleEpisodeWatched}
          />
        )}

        {/* 需要向上滚动才能看到的内容 */}
        <div className={isDrama ? styles.extraInfoSection : ''}>
          {/* 演员列表 */}
          <div className={styles.castSection}>
            <h3 className={styles.sectionTitle}>演员人员</h3>
            <div className={styles.castList}>
              {videoData.cast.map((member) => (
                <TVFocusable
                  key={member.id}
                  id={`tv-focus-videoDetails-cast-${member.id}`}
                  row={2}
                  col={parseInt(member.id)}
                  onClick={() => {}}
                  className={styles.castItem}
                  currentItem={handleFocusScroll}
                >
                  {member.avatar ? (
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className={styles.castAvatar}
                    />
                  ) : (
                    <div className={styles.castAvatar}>{member.name.charAt(0)}</div>
                  )}
                  <div className={styles.castName}>{member.name}</div>
                  <div className={styles.castRole}>{member.role}</div>
                </TVFocusable>
              ))}
            </div>
          </div>

          {/* 文件信息 */}
          {classes === '电影' && (
            <div className={styles.fileInfo}>
              <div className={styles.fileInfoItem}>文件路径：{CommonUtils.formatFilePath(videoData.filePath)}</div>
              <div className={styles.fileInfoItem}>文件大小：{videoData.fileSize}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoDetails;