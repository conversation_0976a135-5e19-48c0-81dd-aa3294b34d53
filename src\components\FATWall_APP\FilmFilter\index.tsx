import { useInViewport, useControllableValue } from 'ahooks';
import clear from '@/Resources/filmWall/clear.png';
import { Dispatch, SetStateAction, useCallback, useMemo, useRef } from 'react';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import { useTheme } from '@/utils/themeDetector';
import arrow from '@/Resources/layout/arrow.png';
import arrow_dark from '@/Resources/layout/arrow_dark.png';
import useRowDragScroll from '@/hooks/useDragScroll';
import { defaultFiltersByPc, defaultFiltersByApp, IFilterTypeList } from '@/components/FATWall_APP/FATWALL_CONST';
import { px2rem } from '@/utils/setRootFontSize';

interface IFilmFilter {
  controlFilters: Dispatch<SetStateAction<{ [key: string]: string }>>;
  filters: { [key: string]: string };
  filterTypeList: IFilterTypeList[];
  value?: boolean;
  onChange?: (v: any) => void;
  type?: 'app' | 'pc';
  isDisabled?: boolean;
  isLibrary?: boolean; // 是否为库页面 pc端的媒体库需要有收藏、已观看筛选项
}

const defaultTypeList = [
  { key: 'movie', title: '电影' },
  { key: 'tv', title: '电视剧' },
  { key: 'more', title: '更多', type: 'more' },
]

const RowDragWrapper = (props: { type: 'app' | 'pc', filters: { [key: string]: string }, item: IFilterTypeList, selectCallback: (type: string, key: string) => void }) => {
  const { type, filters, item, selectCallback } = props;
  const { rowRef, handleMouseDown, isDragging } = useRowDragScroll();
  return (
    <div className={styles.collapse_filter_type_scroll_container} ref={rowRef} onMouseDown={handleMouseDown} style={{ cursor: isDragging ? 'grabbing' : 'grab' }}>
      <div className={`${styles.collapse_filter_type_content} ${styles[type]}`}>
        {
          item.typeList.map((it) => (
            <div style={{ fontSize: item.type === 'year' && it.key !== '1990s' && it.key !== '1980s' && it.key !== 'other-year' ? px2rem('11px') : '' }}
              key={it.key} className={`${styles.collapse_filter_type_item} ${styles[type]} ${filters[item.type] && filters[item.type] === it.key ? styles.selected : ''}`}
              onClick={() => selectCallback(item.type, it.key)}>
              {it.title}
            </div>
          ))
        }
      </div>
    </div>
  )
}

const FilmFilter = (props: IFilmFilter) => {
  const { filters, controlFilters, filterTypeList, type = 'app', isDisabled, isLibrary } = props;
  const [collapse, setCollapse] = useControllableValue(props)
  const { isDarkMode } = useTheme();
  const visRef = useRef(null);

  const callback = useCallback((entry) => {
    if (!entry.isIntersecting) {
      setCollapse(true);
    }
  }, [setCollapse]);

  useInViewport(visRef, { callback: callback });

  // 选择回调
  const selectCallback = useCallback((type: string, key: string) => {
    controlFilters((filter: any) => {
      let f = { ...filter };
      f[type] = key;
      return f;
    })
  }, [controlFilters])

  const filterLabel = useMemo(() => {
    // 筛选label
    let categoryFilterLabel = '';
    if (filters['classes'] === 'all-classes') {
      categoryFilterLabel = '全部'
    } else {
      categoryFilterLabel = defaultTypeList.find(it => it.key === filters['classes'])?.title || '';
    }
    const otherTypeFilterLabels = Object.keys(filters).map((key: string) => filterTypeList.find((it) => it.type === key)?.typeList.find((it) => it.key === filters[key])).map((it) => it?.title).filter((it) => it && it !== '全部');
    return `${categoryFilterLabel === '全部' ? '' : `${categoryFilterLabel}${otherTypeFilterLabels.length > 0 ? '·' : ''}`}${otherTypeFilterLabels.join('·')}`;
  }, [filterTypeList, filters])

  const clearSelect = useCallback(() => {
    setCollapse((c: boolean) => !c);
    if (type === 'pc') {
      if (isLibrary) {
        controlFilters(defaultFiltersByApp);
        return;
      }
      controlFilters(defaultFiltersByPc);
      return;
    }
    controlFilters(defaultFiltersByApp);
  }, [setCollapse, type, controlFilters, isLibrary])

  const filterTypeAllRender = useCallback((item) => {
    const tempObj: { [key: string]: { key: string, title: string } } = {
      classes: { key: 'all-classes', title: '全部' },
      resolution: { key: 'all-resolution', title: '全部' },
      hdr: { key: 'all-hdr', title: '全部' },
      region: { key: 'all-region', title: '全部' },
      year: { key: 'all-year', title: '全部' },
      kind: { key: 'all-kind', title: '全部' },
      collect: { key: 'all-collect', title: '全部' },
    }
    if (type === 'pc' && !isLibrary) delete tempObj['collect'];
    return (
      <div key={tempObj[item.type].key} className={`${styles.collapse_filter_type_item} ${filters[item.type] && filters[item.type] === tempObj[item.type].key ? styles.selected : ''}`}
        onClick={() => selectCallback(item.type, tempObj[item.type].key)}>
        {tempObj[item.type].title}
      </div>
    )
  }, [filters, isLibrary, selectCallback, type])

  return (
    <>
      <div className={`${styles.collapse_filter_container} ${collapse ? styles.sticky_header : ''} ${styles.collapse_filter_sticky_header_bg} ${styles[type]}`}>
        <div className={`${styles.collapse_filter_type_container} ${isDisabled ? styles.disable : ''}`} style={{ padding: type === 'pc' ? `${px2rem('10px')} ${px2rem('12px')}` : '' }}>
          {
            type === 'app' ? (
              collapse && !Object.keys(filters).every((key) => filters[key].split('-')[0] === 'all') ? <>
                <div className={`${styles.collapse_filter_type_item_label} ${styles[type]} ${styles.selected}`} onClick={clearSelect}>
                  <span>{filterLabel}</span>
                  <PreloadImage className={styles.collapse_filter_type_item_img} src={clear} alt='clear' />
                </div>
                <div className={`${styles.collapse_filter_type_item} ${styles.collapse_filter_type_more}`} onClick={() => setCollapse((c: boolean) => !c)}>
                  更多<PreloadImage className={styles.collapse_filter_type_item_img} style={{ transform: !collapse ? 'rotate(0deg)' : 'rotate(180deg)' }} src={isDarkMode ? arrow_dark : arrow} alt='more' />
                </div>
              </> :
                <>
                  {filterTypeAllRender({ type: 'classes' })}
                  <div className={styles.collapse_filter_type_scroll_container}>
                    <div className={`${styles.collapse_filter_type_content} ${styles[type]}`}>
                      {
                        defaultTypeList.map((it) => {
                          let flag: boolean = false;
                          if (it.type && it.type === 'more') {
                            flag = true;
                          }
                          return (
                            <div key={it.key} className={`${styles.collapse_filter_type_item} ${flag ? styles.collapse_filter_type_more : ''}  ${styles[type]}  ${filters['classes'] && filters['classes'] === it.key ? styles.selected : ''}`}
                              onClick={() => flag ? setCollapse((c: boolean) => !c) : selectCallback('classes', it.key)}>
                              {it.title}
                              {flag ? <PreloadImage className={styles.collapse_filter_type_item_img} style={{ transform: !collapse ? 'rotate(0deg)' : 'rotate(180deg)' }} src={isDarkMode ? arrow_dark : arrow} alt='more' /> : <></>}
                            </div>
                          )
                        })
                      }
                    </div>
                  </div>
                </>
            ) : (
              <>
                {
                  collapse && !Object.keys(filters).every((key) => filters[key].split('-')[0] === 'all') ? (
                    <div className={`${styles.collapse_filter_type_item_label} ${styles.selected}`} onClick={clearSelect}>
                      <span>{filterLabel}</span>
                      <PreloadImage className={styles.collapse_filter_type_item_img} src={clear} alt='clear' />
                    </div>
                  ) : (
                    <></>
                  )
                }
              </>
            )
          }
        </div>
      </div>

      {/* 筛选div */}
      <div className={`${styles.collapse_filter_container} ${collapse ? styles.collapse : styles.expand} ${isDisabled ? styles.disable : ''}`} ref={visRef}>
        {
          type === 'app' ? (
            filterTypeList.map((item) => (
              <div className={styles.collapse_filter_type_container} key={item.type}>
                {filterTypeAllRender(item)}
                <RowDragWrapper type={type} filters={filters} item={item} selectCallback={selectCallback} />
              </div>
            ))
          ) : (
            filterTypeList.filter(item => isLibrary ? item : item.type !== 'collect').map((item) => (
              <div className={styles.collapse_filter_type_container} key={item.type}>
                {filterTypeAllRender(item)}
                <RowDragWrapper type={type} filters={filters} item={item} selectCallback={selectCallback} />
              </div>
            ))
          )
        }
      </div>
    </>
  )
}

export default FilmFilter;