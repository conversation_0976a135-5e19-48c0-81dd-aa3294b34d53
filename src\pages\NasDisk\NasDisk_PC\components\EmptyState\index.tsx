import React from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import emptyStateIcon from '@/Resources/nasDiskImg/EmptyState.png';

interface IEmptyState {
  title?: string;
  description?: string;
  icon?: string;
  className?: string;
  children?: React.ReactNode;
}

const EmptyState: React.FC<IEmptyState> = ({ 
  title = '暂无内容', 
  description,
  icon,
  className,
  children 
}) => {
  return (
    <div className={`${styles.empty_state_container} ${className || ''}`}>
      <div className={styles.empty_state_icon}>
        <PreloadImage src={icon || emptyStateIcon} alt="空状态图标" />
      </div>
      <div className={styles.empty_state_content}>
        <h3 className={styles.empty_state_title}>{title}</h3>
        {description && (
          <p className={styles.empty_state_description}>
            {description}
          </p>
        )}
        {children && (
          <div className={styles.empty_state_actions}>
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmptyState;
