// 摄像机弹窗导入
import Modal from '@/components/Modal';
import { px2rem } from '@/utils/setRootFontSize';

// 摄像机组件导入
import IPCDesktopCameraDetail from "@/pages/IPC/IPC_PC/CameraDetail";
import IPCDesktopDeviceDetail from "@/pages/IPC/IPC_PC/CameraDetail/DeviceDetail";
import AddDevice from "@/pages/IPC/IPC_PC/AddDevice";
import SupportInformation from "@/pages/IPC/IPC_PC/SupportInformation";

// 类型引入
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { ICollapsePanel } from "../Layout";

// 图片引入
import camera_add from "@/Resources/layout/camera_add.png";
import camera_add_dark from "@/Resources/layout/camera_add_dark.png";
import close from "@/Resources/icon/close.png";
import close_white from "@/Resources/icon/close_white.png";
import deviceDetailIcon from "@/Resources/modal/deviceDetail.png";
import deviceDetailIcon_dark from "@/Resources/modal/deviceDetail_dark.png";
import selectIcon from "@/Resources/player/dateSelect.png";

// 常规引入
import { useCallback, useEffect, useMemo, useState } from "react"
import DevicePlugin from '@/components/CameraPlayer/components/plugin/DevicePlugin';
import PopoverSelector from '@/components/PopoverSelector';
import { useRouteMatch } from "react-router-dom";
import { useTheme } from "@/utils/themeDetector";
import { PreloadImage } from "@/components/Image";
import styles from '../Layout.module.scss';
import { useRequest } from 'ahooks';
import { cameraIconInfo } from "@/components/CameraPlayer/constants";
import { getSupportCameraList, listRecordCamera } from '@/api/ipc';

interface ICollapseExtra {
  label: string,
  icon: {
    light: string,
    dark: string
  }
}

export interface ICameraInfoList {
  model_name: string;
  model: string;
  icon: string;
}

const useIPCSideBar = () => {
  // 摄像机弹窗
  const [isShow, setIsShow] = useState<boolean>(false);
  const [isDeviceDetailShow, setIsDeviceDetailShow] = useState<boolean>(false);

  // 添加设备弹窗和支持的摄像机弹窗
  const [showAddDevice, setShowAddDevice] = useState<boolean>(false);
  const [showSupportInfo, setShowSupportInfo] = useState<boolean>(false);

  const [cameras, setCameras] = useState<(ICollapsePanel & ICameraDetail)[]>([]);

  const [curKey, setCurKey] = useState<string>('');
  const [isShowSelector, setIsShowSelector] = useState<boolean>(false);

  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  // 摄像机机型信息
  const [cameraInfoList, setCameraInfoList] = useState<ICameraInfoList[]>([]);

  const getModel = useCallback((model: string) => {
    const item = cameraInfoList.find((it) => it.model === model);
    return item || undefined;
  }, [cameraInfoList])

  useRequest(getSupportCameraList, {
    manual: false,
    onError(e) {
      console.log('error:', e);
    },
    onSuccess(data: any) {
      if (data.code === 0 && data.data) {
        setCameraInfoList(data.data.camera.map((it: ICameraInfoList) => {
          const origin = window.location.origin;
          const pathname = window.location.pathname;
          const needPath = pathname.split('/').slice(2, 5).join('/'); // 海康路径中带有/E:/
          return {
            ...it,
            icon: `${origin}/${needPath}/${it.icon}`,
          }
        }));
      }
    }
  })

  const { run: listRecordRun } = useRequest(listRecordCamera, {
    manual: true,
    onError: (e) => {
      console.log('error', e);
    },
    onSuccess: (data) => {
      if (data.code === 0 && data.data.camera) {
        const cameraList = data.data.camera.map((it: ICameraDetail) => {
          const model = getModel(it.model);
          const icon = model ? model.icon : cameraIconInfo(it.model);
          return { ...it, label: model ? model.model_name : it.name, key: it.did, name: it.name, icon: icon }
        })
        setCameras(cameraList);
        console.log('查询成功,摄像头列表:', cameraList);
      }
    }
  })

  useEffect(() => {
    if (cameraInfoList.length !== 0) {
      listRecordRun({ did: [] });
    }
  }, [cameraInfoList, cameraInfoList.length, listRecordRun])

  // 弹窗设备切换选项
  const selectOptions = useMemo(() => {
    return cameras.map((item) => {
      return {
        label: item.name,
        value: item.key,
        icon: item.icon,
        subtitle: item.label
      }
    })
  }, [cameras])

  const cameraItem: (ICollapsePanel & ICameraDetail) | undefined = useMemo(() => {
    return cameras.find((item) => item.key === curKey);
  }, [cameras, curKey])

  // 摄像机详情数据
  const modalLeftContent = useMemo(() => {
    return (
      <div className={styles.modal_left}>
        {
          cameraItem && (
            <>
              <DevicePlugin title={cameraItem.name} subtitle={cameraItem.name} titleColor="var(--text-color)" subtitleColor="var(--subtitle-text-color)" icon={cameraItem.icon} />
              <PopoverSelector visible={isShowSelector} onVisibleChange={setIsShowSelector} onChange={(value) => setCurKey(value)} options={selectOptions} value={cameraItem.key}>
                <div className={styles.selectIcon}>
                  <PreloadImage src={selectIcon} alt="select" />
                </div>
              </PopoverSelector>
            </>
          )
        }
      </div>
    )
  }, [cameraItem, isShowSelector, selectOptions])

  // 弹窗右侧内容
  const modalRightContent = useMemo(() => (
    <div className={styles.modal_right}>
      <PreloadImage onClick={() => setIsDeviceDetailShow(true)} src={isDarkMode ? deviceDetailIcon_dark : deviceDetailIcon} alt="deviceDetail" />
    </div>
  ), [isDarkMode])

  // 摄像机初始化数据
  const curDeviceStartLive = useCallback(async () => {
    const cameraList = [...cameras];
    if (cameraList.length === 0) return;
    const camera_lens: string[] = [];
    cameraList.forEach((item) => {
      item.key_frame.forEach((it) => {
        camera_lens.push(`${item.did}_${it.lens_id}`)
      })
    });
  }, [cameras])

  // 呼出弹窗，设备详情
  const showDeviceDetail = useCallback((rcd) => {
    console.log('查看设备详情,当前设备key:', rcd.key);
    setCurKey(rcd.key);
    setIsShow(true);
  }, [])

  useEffect(() => {
    curDeviceStartLive();
  }, [curDeviceStartLive])

  const handleCollapsePanelClick = useCallback((item: { label: string; icon: { light: string; dark: string } }) => {
    if (item.label === '添加摄像机') {
      setShowAddDevice(true);
    }
  }, []);

  // 折叠面板模板
  const panelComponents = useCallback((items: ICollapsePanel[], extra: ICollapseExtra[], callback: ((item: ICollapsePanel) => void), extraCallback: (item: ICollapseExtra) => void) => {
    return <div className={`${styles.collapse_content}`}>
      {
        items.map((item) => (
          <div key={item.key} className={`${styles.collapse_content_item}`} onClick={() => callback(item)}>
            <PreloadImage src={item.icon} alt="camera_icon" />
            <span>{item.label}</span>
          </div>
        ))
      }
      {
        extra.map((item) => (
          <div
            key={item.label}
            className={`${styles.collapse_content_item}`}
            onClick={() => extraCallback(item)}
          >
            <PreloadImage src={isDarkMode ? item.icon.dark : item.icon.light} alt="collapsePanel_icon" />
            <span style={{ color: 'var(--primary-color)' }}>{item.label}</span>
          </div>
        ))
      }
    </div>
  }, [isDarkMode]);

  // 配置侧边展开面板
  const collapsePanel = useMemo(() => {
    const type = path.split('/')[1];
    switch (type) { // 根据当前路径展示不同的折叠面板数组
      case 'cameraManagement_pc': return [
        {
          key: '1', label: '全部摄像机', children: panelComponents(
            cameras,
            [
              { label: '添加摄像机', icon: { light: camera_add, dark: camera_add_dark } }
            ],
            showDeviceDetail,
            handleCollapsePanelClick)
        }
      ]
    }
  }, [cameras, handleCollapsePanelClick, panelComponents, path, showDeviceDetail])

  const modalComponent = useMemo(() => {
    return (
      <>
        {/* modal放置区域 */}

        {/* 摄像机详情弹窗 */}
        <Modal contentStyle={{ width: px2rem("1200px"), height: px2rem("724px") }} isShow={isShow} content={<IPCDesktopCameraDetail cameraItem={cameraItem} />}
          left={modalLeftContent} right={modalRightContent} onCancel={() => { console.log('close'); setIsShow(false); }} footer={null} />

        {/* 摄像机配置弹窗 */}
        <Modal contentStyle={{ width: px2rem("835px"), height: px2rem("580px") }} isShow={isDeviceDetailShow}
          onCancel={() => setIsDeviceDetailShow(false)} content={<IPCDesktopDeviceDetail onDestroy={() => { setIsDeviceDetailShow(false); setIsShow(false) }}
            item={cameraItem} />} title="设备详情" backIcon={isDarkMode ? close_white : close} footer={null} />

        {/* 添加设备弹窗 */}
        {showAddDevice && (
          <AddDevice
            visible={true}
            onClose={() => setShowAddDevice(false)}
          />
        )}

        {/* 支持的摄像机弹窗 */}
        {
          showSupportInfo && (
            <SupportInformation
              visible={true}
              onClose={() => setShowSupportInfo(false)}
            />
          )
        }
      </>
    )
  }, [cameraItem, isDarkMode, isDeviceDetailShow, isShow, modalLeftContent, modalRightContent, showAddDevice, showSupportInfo])

  return {
    modalComponent,
    collapsePanel,
    cameras,
    setCameras,
    cameraInfoList,
    showDeviceDetail
  }
}

export default useIPCSideBar;