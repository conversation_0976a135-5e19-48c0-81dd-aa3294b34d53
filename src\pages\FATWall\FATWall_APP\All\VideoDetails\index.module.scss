.container {
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
  max-height: 100vh;
  height: 100%;
  padding-bottom: 20px;
  position: relative;
  overflow: hidden; // 防止内容溢出

  // 将导航栏样式移到container内部，使其只在组件内生效
  :global(.adm-nav-bar) {
    --adm-color-text: #FFFFFF;
    --adm-font-size-7: 16px;
  }

  :global(.adm-nav-bar-back-arrow) {
    color: #FFFFFF;
    font-size: 20px;
  }

}

// 添加滚动容器
.scrollContainer {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // 为iOS提供平滑滚动
  height: 100%;
  
  // 设置滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
  }
}

// 顶部背景和电影信息
.headerSection {
  position: relative;
  height: 300px;
  background-size: cover;
  background-position: center;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.posterOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);

  // 将导航栏位置样式放在这里，使其定位在顶部
  :global(.adm-nav-bar) {
    position: fixed;
    top: 35px;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: transparent;
  }
}

.movieInfo {
  position: relative;
  z-index: 1;
}

.title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  word-wrap: break-word; // 允许长单词换行
  word-break: break-all; // 在任何字符间换行（适用于中文）
  line-height: 1.3; // 设置行高
}

.metaInfo {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  flex-wrap: wrap; // 允许换行
  line-height: 1.4; // 设置行高，让换行后的内容有适当间距
}

.score {
  color: #3482FF;
  font-weight: bold;
}

.divider {
  margin: 0 8px;
  color: #fff;
}

.sourceInfo {
  font-size: 12px;
  color: #fff;
}

// 画质标签
.qualityTags {
  display: flex;
  padding: 16px;
  gap: 8px;
  flex-wrap: wrap; // 允许标签换行
}

.qualityTag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  background-color: transparent;
  border: 1px solid #fff;
}

// 版本选择器
.versionSelector {
  padding: 0 16px 16px;

}

.versionSelectorContent{
// 嵌套的选择器，使样式只在组件内部生效
  :global(.adm-popover-inner) {
    background-color: #525252 !important;
    border-radius: 15px !important;
  }

}
  

.versionButton {
  border-radius: 10px;
  font-size: 14px;
  background-color: transparent;
  border: 1px solid #555;
  color: #fff;
  padding: 6px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  max-width: 300px;

  .versionButtonText {
    flex: 1;
  }

  .versionButtonIcon {
    padding: 2px;
    width: 22px;
    height: 22px;
  }
}

.versionList {
  padding: 8px 0;
  background-color: #525252;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  min-width: 240px;
}

.versionItem {
  padding: 8px 16px;
  color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .versionItemText{
    flex:1;
  }
  .versionItemCheckIcon{
    width: 20px;
    margin-left: 20px;
    // color: #0080FF !important;
    font-size: 16px;
  }
}

.versionItemSelected {
  color: #0080FF;
}

.versionCheckIcon {
  margin-right: 8px;
  color: #0080FF !important;
  font-size: 16px;
  // width: 20px;
  // height: 20px;
  // font-weight: bold;
  // border: 2px solid #0080FF;
}

.versionDivider {
  margin: 0 auto !important;
  border-color: #666666;
  width: 90%;
}

// 操作按钮
.actionButtons {
  display: flex;
  padding: 0 16px 16px;
  gap: 10px;
  flex-wrap: wrap; // 允许按钮换行
}


.playButton {
  // flex: 1;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 140px;
  height: 57px;
  background-color: #1677ff;
  .playfont{
    font-size:14px
  }
}

.iconButton {
  width: 57px;
  height: 57px;
  border-radius: 8px;
  background-color: #000;
  border: 2px solid #555;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}



// 影片简介
.synopsis {
  padding: 16px;
  font-size: 14px;
  color: #B2B2B2;
  position: relative;
}

.synopsisText {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  line-height: 1.5;
}

.synopsisTextFull {
  line-height: 1.5;
}

.more {
  color: #1677ff;
  margin-left: 4px;
  display: inline-block;
}

// 演员列表
.castSection {
  padding: 16px;
  overflow: hidden;
}

.sectionTitle {
  font-size: 16px;
  margin-bottom: 16px;
}

.castList {
  display: flex;
  overflow-x: auto;
  gap: 16px;
  padding-bottom: 8px;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

.castItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
}

.actorAvatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #555;
  margin-bottom: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.actorName {
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 4px;
  text-align: center;
  color: #ffffff;
  line-height: 1.2;
  word-wrap: break-word; // 允许长名字换行
  word-break: break-all; // 在任何字符间换行
}

.actorRole {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.2;
  word-wrap: break-word; // 允许长角色名换行
  word-break: break-all; // 在任何字符间换行
}

// 文件信息
.fileInfoSection {
  padding: 16px;
}

.fileInfoBox {
  background-color: #333333;
  border-radius: 8px;
  padding: 16px;
}

.fileInfoItem {
  margin-bottom: 12px;
}

.fileInfoItem:last-child {
  margin-bottom: 0;
}

.fileInfoLabel {
  font-size: 14px;
  color: #C1C1C1;
  margin-bottom: 4px;
}

.fileInfoValue {
  font-size: 14px;
  color: #C1C1C1;
  word-break: break-all;
}

// 右侧图标的样式
.right {
  img {
    height: 26px;
    margin: 0 20px;
    filter: brightness(0) invert(1); // 将图标转换为白色
  }
}

// 更多选项Popover样式
.morePopoverContainer {
  :global(.adm-popover-inner) {
    border-radius: 15px !important;
  }
}

.morePopover {
  min-width: 160px;
}

.morePopoverItem {
  padding: 14px 18px;
  display: flex;
  align-items: center;
  color: #000000;
  transition: background-color 0.2s;
  
  &:hover, &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:first-child {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
  }
  
  &:last-child {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
  }
}

.morePopoverText {
  font-size: 16px;
}

.morePopoverDivider {
  margin: 0 auto !important;
  border-color: #666666 !important;
  width: 90%;
}

.dramaInfo {
  padding: 16px;
  overflow-x: auto;
  // height: 150px;
  scrollbar-width: none;
}
.dramaInfo::-webkit-scrollbar {
  height: 0;
  display: none;
}


// 修正匹配信息界面的样式
.matchCorrectionOverlay {
  position: fixed;
  top: 35px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.searchContainer {
  padding: 4px 8px 8px 12px;
  display: flex;
  align-items: center;
  background-color: var(--background-color);
}

.searchInputWrapper {
  flex: 1;
  background-color: var(--event-card-background-color);
  border-radius: 16px;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
}

.searchIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px 0 10px;
}

.searchIcon {
  font-size: 18px;
  color: var(--list-value-text-color);
}

.searchInput {
  --font-size: 16px;
  --color: var(--text-color);
  --placeholder-color: var(--thinLine-background-color);
  height: 44px;
  flex: 1;
  
  :global {
    .adm-input {
      background-color: transparent;
    }
    
    .adm-input-element {
      font-size: 16px;
      padding-left: 4px;
    }
  }
}

.cancelButton {
  padding: 0 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
  cursor: pointer;
  white-space: nowrap;
}

.searchResults {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 搜索状态容器 */
.searchStateContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: 20px;
  :global{
    .adm-button:active::before{
      opacity: 0;
    }
    .adm-error-block-image{
      max-width: 67px;
      max-height: 67px;
    }
    .adm-error-block-description-title{
      color: var(--list-value-text-color);
    }
    .adm-error-block-image{
      max-width: 120px;
    }
    .adm-error-block-description{
      margin-top: 18px;
    }
  }
}

/* 加载状态 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 16px;
  color: var(--text-color);
}

.retryButton {
  font-size: 16px;
  background-color: var(--cancel-btn-background-color);
  border-radius: 16px;
  margin-top: 10px;
}

/* 搜索结果列表 */
.searchResultList {
  display: flex;
  flex-direction: column;
  // gap: 16px;
}

.searchResultItem {
  display: flex;
  padding: 12px 0;
  background-color: var(--background-color);
  cursor: pointer;
  position: relative;
}

.resultPoster {
  width: 120px;
  height: 150px;
  overflow: hidden;
  margin-right: 13px;
  flex-shrink: 0;
  border-radius: 8px;
  padding: 0 5px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }
}

.resultInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.resultTitle {
  font-size: 17px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 12px;
  line-height: 23px;
}

.resultMeta {
  font-size: 14px;
  color: var(--list-value-text-color);
  margin-bottom: 6px;
  line-height: 19px;
  span{
    color: var(--title-color);
    font: MiSans;
  }
}

.resultSelect {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  margin-left: 8px;
}

/* 添加自定义圆形选择器样式 */
.customRadio {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
  &.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    
    .innerDot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #fff;
    }
  }
}

/* 确认按钮容器 */
.confirmButtonContainer {
  padding: 28px 28px 80px;
  background-color: var(--background-color);
}

.confirmButton {
  height: 50px;
  font-size: 16px;
  border-radius: 16px;
  font-weight: 500;
  background-color: var(--primary-color);
}

//删除
.modalButton {
  width: 100%;
  height: 50px;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
  cursor: pointer;

  &:hover {
    color: #4096ff;
  }
}
.progressModal{
  text-align: left;
  margin-bottom: 15px;
  font-size: 16px;
  color: var(--text-color);
}
.actorAvatar{
  width: 66px;
  height: 66px;
  border-radius: 50%;
  background-color: #555;
  margin-bottom: 8px;
}
