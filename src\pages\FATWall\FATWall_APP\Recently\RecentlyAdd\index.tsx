import React, { useState, useRef, useEffect, useCallback } from "react";
import { useTheme } from "@/utils/themeDetector";
import NavigatorBar from "@/components/NavBar";
import { useRequest } from 'ahooks';

import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import FilmCard from "../../../../../components/FATWall_APP/FilmCard";
import { useHistory } from "react-router-dom";
import { getRecentlyAdd, mediaProps } from "@/api/fatWall";
import { px2rem } from "@/utils/setRootFontSize";
import { formatTimeAgo } from "../RecentlyPlay";

export default function RecentlyAdd() {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialLoaded, setInitialLoaded] = useState(false);
  const filmsContainerRef = useRef<HTMLDivElement>(null);

  const defaultRecentlyAddList: (mediaProps & { selected?: boolean })[] = [];

  const [recentlyAddList, setRecentlyAddList] = useState<(mediaProps & { selected?: boolean })[]>(defaultRecentlyAddList);

  const { run: fetchRecentlyAdd } = useRequest(
    (params) => getRecentlyAdd(params), 
    {
      manual: true,
      onSuccess: (res) => {
        const newData = (res.data as any).medias.map((item: mediaProps) => ({ ...item, selected: false }));
        if (offset === 0) {
          setRecentlyAddList(newData);
          setInitialLoaded(true);
          setHasMore(newData.length === 50);
        } else {
          setRecentlyAddList(prev => [...prev, ...newData]);
          setHasMore(newData.length === 50);
        }
        setLoading(false);
      },
    }
  );

  useEffect(() => {
    fetchRecentlyAdd({ offset: 0, limit: 50 });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadMore = useCallback(() => {
    if (!hasMore || loading) return;
    setLoading(true);
    const newOffset = offset + 50;
    setOffset(newOffset);
    fetchRecentlyAdd({ offset: newOffset, limit: 50 });
  }, [offset, hasMore, loading, fetchRecentlyAdd]);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      if (!filmsContainerRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = filmsContainerRef.current;
      // 只有初始50条数据加载完成，并且滚动到底部时才加载更多
      if (initialLoaded && scrollHeight - scrollTop - clientHeight < 20 && hasMore && !loading) {
        loadMore();
      }
    };

    const container = filmsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, loading, loadMore, initialLoaded]);

  const arrowIcon = isDarkMode ? arrowLeftDark : arrowLeft;

  return (
    <div className={styles.container}>
      <NavigatorBar
        backIcon={arrowIcon}
        onBack={() => history.goBack()}
      />
      <div className={styles.title}>最近添加</div>
      <div className={styles.filmsContainer} ref={filmsContainerRef}>
        {recentlyAddList.length > 0 ? (
          recentlyAddList.map((item) => (
            <div key={item.media_id} className={styles.filmItem}>
              <FilmCard
                poster={item.poster ? item.poster.length > 0 ? item.poster.length > 1 ? item.poster[1] : item.poster[0] : '' : ''}
                title={item.origin_name || item.trans_name || ''}
                time={formatTimeAgo(item.create_time)}
                type="add"
                layout="horizontal"
                options={{
                  style: { width: px2rem("144px"), height: px2rem("82px") },
                  callback: () => console.log(`Clicked on ${item.origin_name || item.trans_name}`),
                }}
              />
            </div>
          ))
        ) : (
          <div className={styles.noDataTip}>暂无数据</div>
        )}
      </div>
    </div>
  );
}
