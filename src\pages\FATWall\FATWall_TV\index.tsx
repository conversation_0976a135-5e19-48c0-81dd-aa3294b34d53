import React, { createContext, FC, useCallback, useContext, useEffect, useState } from "react";
import FilmSideBar from "./SideBar";
import styles from './index.module.scss';
import RecentlyPlay from "./optPages/RecentlyPlay";
import { Route, Switch, useRouteMatch } from "react-router-dom";
import AllRecentlyPlay from "./AllRecentlyPlay";
import RecentlyAdd from "./RecentlyAdd";
import AllRecentlyAdd from "./AllRecentlyAdd";
import All from "./optPages/All";
import Played from "./Played";
import Collect from "./Collect";
import Library from "./Library";
import SearchByTV from "./Search";
import DramasOrMovie from "./DramasOrMovie";
import VideoDetails from "./VideoDetails";
import { getFullLibraryData } from "@/api/fatWall";
import { useRequest } from "ahooks";

export const FAT_TV_PREFIX_PATH = '/filmAndTelevisionWall_tv';

const DefaultLayout: FC = ({ children }) => {
  return (
    <>
      <div className={styles["nasTV_sideBar"]}>
        <FilmSideBar />
      </div>
      <div className={styles.nasTV_content}>
        {children}
      </div>
    </>
  )
}

const LibraryContext = createContext<{
  libs: { lib_id: number; name: string; }[],
  setLibs: React.Dispatch<React.SetStateAction<{
    lib_id: number;
    name: string;
  }[]>>,
  refreshLibraries: () => Promise<void>
}>({ libs: [], setLibs: (v) => null, refreshLibraries: async () => { } }); // 媒体库列表

export const useLibraryListTV = () => useContext(LibraryContext);

const NasTV: FC = () => {

  const { path } = useRouteMatch();

  // 获取媒体库列表
  const [libraries, setLibraries] = useState<{
    lib_id: number;
    name: string;
  }[]>([]);

  const { runAsync: getLibraryData } = useRequest(getFullLibraryData, { manual: true, }); // 获取所有媒体库

  const getLib = useCallback(async () => {
    const res = await getLibraryData({ sort_type: 0, asc: 0 }).catch((e) => console.log('查询所有媒体库失败：', e));
    if (res && res.code === 0 && res.data) {
      const myLibs = res.data.my_libs.libs.map((item) => { return { lib_id: item.lib_id, name: item.name } });
      const otherLibs = res.data.share2me.libs.map((item) => { return { lib_id: item.lib_id, name: item.name } });
      const allLibs = myLibs.concat(otherLibs);
      setLibraries(allLibs);
    }
  }, [getLibraryData])

  useEffect(() => {
    // 初始化媒体库
    getLib();
  }, [getLib])

  return (
    <LibraryContext.Provider value={{ libs: libraries, setLibs: setLibraries, refreshLibraries: getLib }}>
      <div className={styles["nasTv_container"]}>
        <Switch>
          <Route exact path={`${path}/allRecentlyPlay`}><AllRecentlyPlay /></Route>
          <Route exact path={`${path}/recentlyAdd/allRecentlyAdd`}><AllRecentlyAdd /></Route>
          <Route exact path={`${path}/search`}><SearchByTV /></Route>
          <Route exact path={`${path}/recentlyAdd`}>
            <DefaultLayout>
              <RecentlyAdd />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/all`}>
            <DefaultLayout>
              <All />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/library/:id`}>
            <DefaultLayout>
              <Library />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/collect`}>
            <DefaultLayout>
              <Collect />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/played`}>
            <DefaultLayout>
              <Played />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/dramasOrMovie`}>
            <DefaultLayout>
              <DramasOrMovie />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/videoDetails`}>
            <DefaultLayout>
              <VideoDetails />
            </DefaultLayout>
          </Route>

          {/* 默认显示页面 */}
          <Route exact path={path}>
            <DefaultLayout>
              <RecentlyPlay />
            </DefaultLayout>
          </Route>

        </Switch>
      </div>
    </LibraryContext.Provider>
  );
}

export default NasTV;
