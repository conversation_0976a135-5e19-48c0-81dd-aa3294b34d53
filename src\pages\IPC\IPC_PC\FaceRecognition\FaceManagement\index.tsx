import React, { useState } from "react";
import { <PERSON><PERSON>, Modal, Input } from "antd";
import { CloseOutlined, PlusOutlined } from "@ant-design/icons";
import { Toast } from "antd-mobile";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import userTest from "@/Resources/camMgmtImg/user-test.png";
import FaceDetail from "../FaceDetail";
import { getFacialInfo, setFacialInfo, FacialInfo } from "@/api/ipc";
import { PreloadImage } from "@/components/Image";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";

interface Face extends FacialInfo {
  id?: string;
}

const FaceManagement: React.FC = () => {
  const [nameModalVisible, setNameModalVisible] = useState<boolean>(false);
  const [faceNameInput, setFaceNameInput] = useState<string>("");
  // const [, setIsEditingName] = useState<boolean>(false);
  const [showDetailView, setShowDetailView] = useState<boolean>(false);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [selectedFace, setSelectedFace] = useState<Face | null>(null);
  const [selectedUnmarkedFace, setSelectedUnmarkedFace] =
    useState<FacialInfo | null>(null);

  const [markedFaces, setMarkedFaces] = useState<FacialInfo[]>([]);

  const [unmarkedFaces, setUnmarkedFaces] = useState<FacialInfo[]>([]);

  // 获取人脸信息，与APP端保持一致
  const { run: fetchFaces } = useRequest(getFacialInfo, {
    onSuccess: (res) => {
      if (res && res.code === 0) {
        // 将人脸分为已标记和未标记两组
        const marked: FacialInfo[] = [];
        const unmarked: FacialInfo[] = [];

        res.data.info.forEach((face) => {
          if (face.name && face.name.trim() !== "") {
            marked.push(face);
          } else {
            unmarked.push(face);
          }
        });

        setMarkedFaces(marked);
        setUnmarkedFaces(unmarked);
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("获取人脸信息失败:", error);
      Toast.show({
        content: "获取人脸信息失败，使用默认数据",
      });
    },
  });

  // 设置人脸信息，与APP端保持一致
  const { run: saveFaceInfo } = useRequest(setFacialInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        Toast.show({
          content: "保存成功",
          position: "bottom",
          duration: 1000,
        });

        // 重新获取人脸列表
        fetchFaces();

        // 关闭弹窗并清空输入
        setNameModalVisible(false);
        setFaceNameInput("");
        setSelectedUnmarkedFace(null);
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("保存人脸信息失败:", error);
      Toast.show({
        content: "保存失败，请重试",
      });
    },
  });

  // 处理添加人脸操作，与APP端保持一致
  // const handleAddFace = () => {
  //   setNameModalVisible(true);
  //   setFaceNameInput("");
  //   setIsEditingName(false);
  //   setShowDetailView(false);
  //   setSelectedUnmarkedFace(null);
  // };

  // 处理头像点击，显示详情视图
  const handleAvatarClick = () => {
    setShowDetailView(true);
  };

  // 处理人脸名称确认，与APP端保持一致
  const handleNameConfirm = () => {
    if (!faceNameInput.trim()) {
      Toast.show({ content: "请输入人脸备注名称", position: "center" });
      return;
    }

    if (selectedUnmarkedFace) {
      // 更新现有的未标记人脸
      saveFaceInfo({
        uuid: selectedUnmarkedFace.uuid,
        name: faceNameInput,
        profile_pic: selectedUnmarkedFace.profile_pic,
        delete: false,
      });
    } else {
      // 仅在添加新人脸时实现
      setNameModalVisible(false);
    }
  };

  // 处理未标记人脸点击事件，与APP端保持一致
  // const handleUnmarkedFaceClick = (face: FacialInfo) => {
  //   setSelectedUnmarkedFace(face);
  //   setFaceNameInput("");
  //   setNameModalVisible(true);
  //   setShowDetailView(false);
  // };

  // 处理人脸点击事件
  const handleFaceItemClick = (face: Face) => {
    // 已标记人脸点击时，设置选中的人脸并显示详情弹窗
    setSelectedFace(face);
    setDetailModalVisible(true);
    // if (face.name && face.name.trim() !== "") {
    //   setSelectedFace(face);
    //   setDetailModalVisible(true);
    // } else {
    //   // 未标记人脸点击时，打开标记弹窗
    //   handleUnmarkedFaceClick(face);
    // }
  };

  // 关闭详情弹窗
  const handleCloseDetailModal = (needRefresh?: boolean) => {
    setDetailModalVisible(false);
    setSelectedFace(null);
    
    // 如果需要刷新，则重新获取人脸列表
    if (needRefresh) {
      fetchFaces();
    }
  };

  // 处理双击编辑备注名称
  // const handleNameDoubleClick = () => {
  //   setIsEditingName(true);
  // };

  // 处理输入框失去焦点
  // const handleInputBlur = () => {
  //   setIsEditingName(false);
  // };

  // 渲染备注名称区域
  // const renderNameSection = () => {
  //   if (isEditingName) {
  //     return (
  //       <Input
  //         className={styles.nameInput}
  //         value={faceNameInput}
  //         onChange={(e) => setFaceNameInput(e.target.value)}
  //         onBlur={handleInputBlur}
  //         onPressEnter={handleInputBlur}
  //         autoFocus
  //       />
  //     );
  //   }
  //   return (
  //     <div className={styles.nameLabel} onDoubleClick={handleNameDoubleClick}>
  //       {faceNameInput || "备注名称"}
  //     </div>
  //   );
  // };

  return (
    <div className={styles.container}>
      {/* <div className={styles.header}>
        <div className={styles.title}>AI功能</div>
      </div> */}

      <div className={styles.addFaceButtonContainer}>
        <button
          className={styles.addFaceButton}
          //  onClick={handleAddFace}
        >
          添加人脸
        </button>
      </div>

      {/* 已标记人脸区域 */}
      <div className={styles.section}>
        {markedFaces.length > 0 && (
          <div className={styles.sectionTitle}>已标记</div>
        )}
        <div className={styles.faceGrid}>
          {markedFaces.map((face) => (
            <div
              key={face.uuid}
              className={styles.faceItem}
              onClick={() => handleFaceItemClick(face)}
            >
              <div className={styles.avatarContainer}>
                <PreloadImage
                  src={splitURL(face.profile_pic)}
                  className={styles.avatar}
                  needHeader={true}
                />
              </div>
              <div className={styles.faceName}>{face.name}</div>
            </div>
          ))}
        </div>
      </div>

      {/* 分隔线 */}
      {markedFaces.length > 0 && <div className={styles.divider}></div>}

      {/* 未标记人脸区域 */}
      {unmarkedFaces.length > 0 && (
        <div className={styles.section}>
          <div className={styles.sectionTitle}>未标记</div>
          <div className={styles.faceGrid}>
            {unmarkedFaces.map((face) => (
              <div
                key={face.uuid}
                className={styles.faceItem}
                onClick={() => handleFaceItemClick(face)}
              >
                <div className={styles.avatarContainer}>
                  <PreloadImage
                    src={splitURL(face.profile_pic)}
                    className={styles.avatar}
                    needHeader={true}
                  />
                </div>
                <div className={styles.faceName}>未标记</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 新建人脸弹窗 - 基本视图 */}
      {!showDetailView && (
        <Modal
          title={null}
          open={nameModalVisible}
          onCancel={() => setNameModalVisible(false)}
          footer={null}
          width={836}
          centered
          closeIcon={null}
          className={styles.addFaceModal}
        >
          <div className={styles.faceDetailModal}>
            <div className={styles.modalHeader}>
              <div
                className={styles.closeButton}
                onClick={() => setNameModalVisible(false)}
              >
                <CloseOutlined
                  style={{ color: "var(--title-color)", width: 16, height: 16 }}
                />
              </div>
              <div className={styles.modalTitle}>
                {selectedUnmarkedFace ? "标记人脸" : "新建人脸"}
              </div>
            </div>

            <div className={styles.modalContent}>
              <div className={styles.leftSection}>
                <div
                  className={styles.avatarContainer}
                  onClick={handleAvatarClick}
                >
                  <img
                    alt=""
                    src={
                      selectedUnmarkedFace
                        ? selectedUnmarkedFace.profile_pic
                        : userTest
                    }
                    className={styles.avatarPlaceholder}
                    onError={(e) => {
                      // 如果图片加载失败，使用默认图片
                      e.currentTarget.src = userTest;
                    }}
                  />
                </div>
                <div className={styles.nameLabel}>
                  <Input
                    className={styles.nameInput}
                    value={faceNameInput}
                    onChange={(e) => setFaceNameInput(e.target.value)}
                    placeholder="请输入人脸备注名称"
                    autoFocus
                  />
                </div>
              </div>

              <div className={styles.rightSection}>
                <div className={styles.linkFaceBox}>
                  <PlusOutlined className={styles.plusIcon} />
                  <div className={styles.linkFaceText}>关联人脸</div>
                </div>
              </div>
            </div>

            <div className={styles.actionButtons}>
              <div></div> {/* Empty div for flex spacing */}
              <Button
                type="primary"
                className={styles.saveButton}
                onClick={handleNameConfirm}
              >
                保存
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* 人脸详情弹窗 */}
      {selectedFace && (
        <FaceDetail
          visible={detailModalVisible}
          faceId={selectedFace.uuid}
          faceName={selectedFace.name}
          profilePic={selectedFace.profile_pic}
          onClose={handleCloseDetailModal}
        />
      )}
    </div>
  );
};

export default FaceManagement;
