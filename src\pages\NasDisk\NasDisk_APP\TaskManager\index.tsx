import { CapsuleTabs, Popover, Toast, Checkbox } from "antd-mobile";
import DownloadTask, { DownloadTaskRef } from "./DownloadTask";
import UploadBaiDu, { UploadBaiDuRef } from "./UploadBaiDu";
import { useHistory, useLocation } from "react-router-dom";
import { useState, useRef } from "react";
import CustomPopup from "../../../../components/CustomPopup";
import styles from "./index.module.scss";
import close from "@/Resources/camMgmtImg/close.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import del from "@/Resources/camMgmtImg/delete.png";
import NavigatorBar from "@/components/NavBar";
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import more from "@/Resources/camMgmtImg/more.png";
import more_dark from "@/Resources/camMgmtImg/more-dark.png";

const actions = [
  { key: "mine", text: "我的" },
  { key: "multiSelect", text: "多选" },
];


const TaskManager = () => {
  const location = useLocation();
  const state = location.state as { selectedIds: number[] } | undefined;
  const selectedIds = state?.selectedIds;
  console.log("selectedIds: ", selectedIds);
  const [isSearch, setIsSearch] = useState(false);
  const [isMultiSelect, setIsMultiSelect] = useState(false);
  const [activeTab, setActiveTab] = useState("management");
  
  // 处理tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换tab时退出多选模式
    if (isMultiSelect) {
      setIsMultiSelect(false);
      setSelectedTasksCount(0);
      setIsSelectAll(false);
    }
  };
  const { isDarkMode } = useTheme();
  // 删除任务相关状态
  const [selectedTasksCount, setSelectedTasksCount] = useState(0);
  const [totalTasksCount, setTotalTasksCount] = useState(0);
  const [deletePopupVisible, setDeletePopupVisible] = useState(false);
  const [deleteLocalFiles, setDeleteLocalFiles] = useState(false);
  const downloadTaskRef = useRef<DownloadTaskRef>(null);
  const uploadBaiDuRef = useRef<UploadBaiDuRef>(null);
  
  // 全选相关状态
  const [isSelectAll, setIsSelectAll] = useState(false);

  const handleClick = (node: any) => {
    console.log("node: ", node);
    if (node.key === "mine") {
      history.push("/baiduNetdisk_app/members");
      setIsSearch(!isSearch);
      if (isMultiSelect) setIsMultiSelect(false);
    } else if (node.key === "multiSelect") {
      setIsMultiSelect(!isMultiSelect);
      if (isSearch) setIsSearch(false);
    } else if (node.key === "settings") {
      history.push("/baiduNetdisk_app/TaskManager/settings");
    }
  };

  const handleEnterMultiSelect = () => {
    setIsMultiSelect(true);
    if (isSearch) setIsSearch(false);
  };

  // 处理任务选择数量变化
  const handleSelectedTasksChange = (count: number) => {
    setSelectedTasksCount(count);
    // 根据选中数量更新全选状态
    setIsSelectAll(count > 0 && count === totalTasksCount);
  };

  // 处理任务总数变化
  const handleTotalTasksChange = (count: number) => {
    setTotalTasksCount(count);
    // 如果总数变化，重新计算全选状态
    setIsSelectAll(selectedTasksCount > 0 && selectedTasksCount === count);
  };

  // 处理全选/取消全选
  const handleSelectAllChange = (checked: boolean) => {
    setIsSelectAll(checked);
    
    // 根据当前活跃的tab调用相应组件的全选功能
    if (activeTab === "management" && downloadTaskRef.current) {
      downloadTaskRef.current.selectAllTasks(checked);
    } else if (activeTab === "upload" && uploadBaiDuRef.current) {
      uploadBaiDuRef.current.selectAllTasks(checked);
    }
  };

  // 处理删除任务按钮点击
  const handleDeleteClick = () => {
    if (selectedTasksCount === 0) {
      Toast.show({
        content: '请至少选择一个任务',
      });
      return;
    }
    setDeletePopupVisible(true);
  };

  // 处理删除取消
  const handleDeleteCancel = () => {
    setDeletePopupVisible(false);
    setDeleteLocalFiles(false);
  };

  // 处理删除确认
  const handleDeleteConfirm = async () => {
    console.log('删除所选任务，同时删除本地文件:', deleteLocalFiles);
    
    try {
      // 根据当前活跃的tab调用相应组件的删除功能
      if (activeTab === "management" && downloadTaskRef.current) {
        await downloadTaskRef.current.deleteSelectedTasks();
      } else if (activeTab === "upload" && uploadBaiDuRef.current) {
        await uploadBaiDuRef.current.deleteSelectedTasks();
      }
      
      setDeletePopupVisible(false);
      setDeleteLocalFiles(false);
      setIsMultiSelect(false);
      setIsSelectAll(false);
    } catch (error) {
      console.error('删除任务失败:', error);
      // Toast 错误信息已经在各自组件中处理了
    }
  };

  const right = (
    <div style={{ fontSize: 24 }}>
      <Popover.Menu
        actions={actions}
        placement="bottom-start"
        onAction={(node) => handleClick(node)}
        trigger="click"
      >
        <PreloadImage src={isDarkMode ? more_dark : more} alt="more" />
      </Popover.Menu>
    </div>
  );
  
  const history = useHistory();
  
  const handleBack = () => {
    if (isMultiSelect) {
      // 如果在编辑态，退出编辑态
      setIsMultiSelect(false);
      setSelectedTasksCount(0);
      setIsSelectAll(false);
    } else {
      // 否则返回上一页
      history.goBack();
    }
  };

  return (
    <div className={styles.taskManagerContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar 
          onBack={handleBack} 
          right={!isMultiSelect ? right : null}
          backIcon={isMultiSelect ? (isDarkMode ? closeDark : close) : undefined}
        />
        <div className={styles.titleContainer}>
          <div className={styles.title}>{isMultiSelect ? ' 删除任务' : '任务管理'}</div>
          {isMultiSelect && (
            <div className={styles.selectAllContainer}>
              <Checkbox
                checked={isSelectAll}
                onChange={handleSelectAllChange}
              />
            </div>
          )}
        </div>
        <div className={styles.taskTab}>
          <CapsuleTabs onChange={handleTabChange}>
            <CapsuleTabs.Tab title="下载至储存" key="management" />
            <CapsuleTabs.Tab
              title="上传至网盘"
              key="upload"
            />
          </CapsuleTabs>
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {activeTab === "management" && (
          <DownloadTask
            ref={downloadTaskRef}
            isMultiSelect={isMultiSelect}
            onEnterMultiSelect={handleEnterMultiSelect}
            onSelectedTasksChange={handleSelectedTasksChange}
            onTotalTasksChange={handleTotalTasksChange}
          />
        )}
        {activeTab === "upload" && (
          <UploadBaiDu
            ref={uploadBaiDuRef}
            isMultiSelect={isMultiSelect}
            onEnterMultiSelect={handleEnterMultiSelect}
            onSelectedTasksChange={handleSelectedTasksChange}
            onTotalTasksChange={handleTotalTasksChange}
          />
        )}
      </div>

      {/* 多选模式下的操作按钮 */}
      {isMultiSelect && (
        <div className={styles.multiSelectActions}>
          <div className={styles.content} onClick={handleDeleteClick}>
            <PreloadImage src={del} alt="del" style={{width: 30, height: 30}}/>
            <span className={styles.delName}>删除</span>
          </div>
        </div>
      )}

      {/* 删除任务确认弹窗 */}
      <CustomPopup
        visible={deletePopupVisible}
        title="删除任务"
        content={`确定要删除所勾选的任务吗?`}
        showCheckbox={true}
        checked={deleteLocalFiles}
        onCheckChange={setDeleteLocalFiles}
        buttons={[
          {
            text: '取消',
            onClick: handleDeleteCancel,
            textColor: '#373737',
          },
          {
            text: '确定',
            color: '#34BBBF',
            type: 'warning',
            textColor: '#fff',
            onClick: handleDeleteConfirm
          }
        ]}
        onClose={handleDeleteCancel}
      />
    </div>
  );
};

export default TaskManager;
