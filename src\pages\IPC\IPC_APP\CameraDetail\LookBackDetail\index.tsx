import { PreloadImage } from "@/components/Image";
import { useCallback, useMemo, useRef } from "react";
import { useHistory, useLocation, useRouteMatch } from "react-router-dom";
import Player from "xgplayer/es/player";
import styles from './index.module.scss';
import share from '@/Resources/icon/share.png';
import del from '@/Resources/icon/delete.png';
import download from '@/Resources/icon/download.png';
import share_dark from '@/Resources/icon/share_white.png';
import del_dark from '@/Resources/icon/delete_white.png';
import download_dark from '@/Resources/icon/download_white.png';
// import detail_dark from '@/Resources/icon/detail_dark.png';
// import detail from '@/Resources/icon/detail.png';
import { IlLookBackData } from "@/components/CameraPlayer/components/plugin/ControlPlugin/PlayerControl";
import MonitorPlayer from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer"
import { deleteToAlbumWithPlayerByApp, downloadAndSaveWithPlayer, downloadAndShareWithPlayer } from "@/api/cameraPlayer";
import { Toast } from "@/components/Toast/manager";
import { useTheme } from "@/utils/themeDetector";
import { modalShow } from "@/components/List";
import { getSystemType } from "@/utils/DeviceType";
import { move2trashbin } from "@/api/fatWall";

interface IBtns {
  label: string,
  name: string,
  icon: string,
  callback: () => void
}

const LookBackDetail = () => {
  const location = useLocation<{ lookBackData: IlLookBackData }>();
  const lookBackData: IlLookBackData = location.state?.lookBackData;
  const cameraRef = useRef<Player | null>(null);
  const { type, url, eventOptions, photoName } = lookBackData;
  const history = useHistory();
  const { path } = useRouteMatch();

  const layoutByType: React.ReactNode = useMemo(() => {
    if (type === 'photo') {
      return <PreloadImage src={url} />
    }
    return eventOptions ? <MonitorPlayer movieTitle={eventOptions.label} urls={eventOptions.urls} playerType={eventOptions ? 'multiple' : 'normal'} cameraRef={cameraRef} baseConfig={{ url: url, type: "Movie", mediaName: "media", }} /> :
      <MonitorPlayer baseConfig={{ url: url, type: "Movie", mediaName: "media" }} cameraRef={cameraRef} />
  }, [eventOptions, type, url])

  const shareCallback = useCallback(async () => {
    await downloadAndShareWithPlayer(JSON.stringify(url), (r) => console.log(r));
  }, [url])

  const delCallback = useCallback(() => {
    let content = null;
    let title = '是否确定删除？';
    const os = getSystemType();
    if (os === 'ios') {
      title = '允许“米家”删除这张照片？';
      content = <>删除的文件将移至“回收站”，保留30天</>;
    }

    if (type === 'photo' && photoName) {
      modalShow(title, content, (async m => {
        await deleteToAlbumWithPlayerByApp(photoName, (res) => {
          if (res && res.code === 0) {
            m.destroy();
            history.push(`${path}/lookBackDetail`);
            Toast.show('删除截图成功');
            return;
          }
          Toast.show('删除截图失败，请稍后再试');
        }).catch(() => {
          Toast.show('删除截图失败，请稍后再试');
        })
      }), () => null, false, { position: 'bottom', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } });
    }

    if (type === 'movie') {
      modalShow(title, content, (async m => {
        const res = await move2trashbin({ path: [url] }).catch((e) => {
          Toast.show('删除失败，请稍后再试');
        });
        if (res && res.code === 0) {
          Toast.show('正在删除');
          return;
        }
      }), () => null, false, { position: 'bottom', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } });
    }

  }, [history, path, photoName, type, url])

  const downloadCallback = useCallback(() => {
    downloadAndSaveWithPlayer([url], (res) => {
      if (res && res.code === 0) {
        Toast.show('正在下载');
      }
    })
  }, [url])

  // const detailList: IListData[] = useMemo(() => {
  //   return [
  //     { key: 'name', type: 'text', label: '文件名称', value: '小米C300' },
  //     { key: 'format', type: 'text', label: '文件格式', value: 'mp4' },
  //     { key: 'encode', type: 'text', label: '视频格式', value: 'H.265' },
  //     { key: 'size', type: 'text', label: '大小', value: '109MB' },
  //     { key: 'location', type: 'text', label: '位置', value: '/存储池1/监控中心' },
  //     { key: 'create_time', type: 'text', label: '录制时间', value: '今天18:23' },
  //     { key: 'type', type: 'text', label: '标签', value: eventOptions?.label || '' },
  //   ]
  // }, [eventOptions?.label])

  // const detailCallback = useCallback(() => {
  //   modalShow('查看详情', <div className={styles.lookBackDetail_modal_container}>
  //     <List dataSource={detailList}></List>
  //   </div>, (m) => m.destroy(), () => null, true, { position: 'bottom', })
  // }, [detailList])

  const { isDarkMode } = useTheme();

  const btns: IBtns[] = useMemo(() => {
    if (type === 'photo') {
      return [
        { label: '下载', name: 'download', icon: isDarkMode ? download_dark : download, callback: downloadCallback },
        { label: '分享', name: 'share', icon: isDarkMode ? share_dark : share, callback: shareCallback },
        { label: '删除', name: 'delete', icon: isDarkMode ? del_dark : del, callback: delCallback },
      ]
    }
    return [
      // { label: '查看详情', name: 'detail', icon: isDarkMode ? detail_dark : detail, callback: detailCallback },
      { label: '下载', name: 'download', icon: isDarkMode ? download_dark : download, callback: downloadCallback },
      { label: '分享', name: 'share', icon: isDarkMode ? share_dark : share, callback: shareCallback },
      { label: '删除', name: 'delete', icon: isDarkMode ? del_dark : del, callback: delCallback },
    ]
  }, [delCallback, downloadCallback, isDarkMode, shareCallback, type])

  return (
    <div className={styles.lookBackDetail_container}>
      {layoutByType}
      <div className={styles.lookBackDetail_btns}>
        {
          btns.map((item: IBtns) => (
            <div key={item.name} className={styles.lookBackDetail_btns_item} onClick={item.callback}>
              <span className={styles.lookBackDetail_btns_item_icon}><PreloadImage src={item.icon} /></span>
              <span className={styles.lookBackDetail_btns_item_label}>{item.label}</span>
            </div>
          ))
        }
      </div>
    </div>
  )
}

export default LookBackDetail;