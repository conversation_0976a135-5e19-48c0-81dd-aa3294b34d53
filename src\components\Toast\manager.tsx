// manager.ts
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { ToastConfig } from './types';
import ToastItem from './index';

interface ToastInstance extends ToastConfig {
  id: string;
  content: React.ReactNode;
}

class ToastManager {
  private container: HTMLDivElement | null = null;
  private toasts: ToastInstance[] = [];

  constructor() {
    this.createContainer();
  }

  private createContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'toast-root';
      document.body.appendChild(this.container);
    }
  }

  private renderToasts() {
    if (!this.container) return;

    ReactDOM.render(<>
      <div className="toast-wrapper">
        {this.toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            {...toast}
            onRemove={() => this.removeToast(toast.id)}
          />
        ))}
      </div>
    </>,
      this.container);
  }

  public show(content: React.ReactNode, config?: ToastConfig): string {
    const id = Math.random().toString(36).slice(2, 9);

    this.toasts = [
      ...this.toasts,
      {
        id,
        content,
        duration: 3000,
        position: 'top',
        ...config
      }
    ];

    if (this.toasts.length > 5) {
      this.toasts = this.toasts.slice(-5);
    }

    this.renderToasts();
    return id;
  }

  public removeToast(id: string) {
    this.toasts = this.toasts.filter(t => t.id !== id);
    this.renderToasts();
  }

  public destroy() {
    if (this.container) {
      ReactDOM.unmountComponentAtNode(this.container);
      document.body.removeChild(this.container);
      this.container = null;
    }
  }
}

export const Toast = new ToastManager();