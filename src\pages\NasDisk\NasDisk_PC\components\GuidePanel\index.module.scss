// PC端引导页面样式
.guidePanel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0px 60px;
  background-color: #fff;
  border-radius: 8px;
}

.guideContent {
  // flex: 1;
  margin-top: 40%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.guideIcons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  gap: 20px;
  
  img {
    width: 200x;
    height: 80px;
    object-fit: contain;
  }
}

.guideTitle {
  font-size: 20px;
  font-family: Misans;
  font-weight: 600;
  color: #000;
  margin-bottom: 17px;
  line-height: 1;
}

.guideDescription {
  font-size: 16px;
  font-family: Misans;
  color: #000;
  font-weight: 400;
  line-height: 1;
  max-width: 699px;
  margin-bottom: 60px;
}


.defaultButton {
  min-width: 340px;
  height: 48px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  background-color: #32BAC0;
  // color:#32BAC0;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba( 64,150,255,0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}
