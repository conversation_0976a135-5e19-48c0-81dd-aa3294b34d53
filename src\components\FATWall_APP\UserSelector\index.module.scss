.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--file-selector-bg);
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;

  .title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color, #000000);
  }
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  color: var(--secondary-text-color, #8C93B0);

  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.userList {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  // 空状态样式
  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 14px;
  }

  :global {
    .adm-list-item {
      padding: 0px 16px;
      line-height: 1;

      .adm-list-item-content-main {
        padding: 0 12px;
      }
    }

    .adm-checkbox-icon {
      background-color: #F0F0F0;
      border: none
    }

    .adm-checkbox {
      --icon-size: 20px;
      --font-size: 14px;
      --gap: 8px;
      --adm-color-primary: #3482FF;
    }

    .adm-list-item-content-prefix {
      padding-right: 0;
    }

    .adm-avatar {
      --size: 50px;
      --border-radius: 50%;
    }

    .adm-list-item {
      background-color: var(--file-selector-bg);
    }
    .adm-list-body{
      border:none
    }
    .adm-checkbox-icon{
      background-color: var(--user-selector-checkbox-bg);
    }
  }
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.userName {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
}



.userGroup {
  font-size: 11px;
  color: var(--tertiary-text-color);
}
.footer {
  padding: 16px;
  margin-bottom: 80px;
  background-color: var(--file-selector-bg);
  display: flex;
  .confirmButton {
    width: 336px;
    height: 50px;
    border-radius: 16px;
    margin: 0 auto;
    font-size: 19px;
    font-weight: 500;
    color: #fff;
    background-color: var(--primary-color);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.5;
    }
  }
}


.userRole {
  font-size: 12px;
  color: var(--subtitle-text-color);
  margin-bottom: 2px;
}