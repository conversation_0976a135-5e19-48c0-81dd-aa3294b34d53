import React, { useState, useMemo } from 'react'
import NavigatorBar from '@/components/NavBar'
import styles from './index.module.scss'

// Mock数据
interface VideoItem {
  id: string
  filename: string
  duration: string
  date: string
  time: string
  size: string
  thumbnail: string
  fullDate: Date
}

const mockVideoData: VideoItem[] = [
  {
    id: '1',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '05/12',
    time: '上午 10:34',
    size: '2.54MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T10:34:00')
  },
  {
    id: '2',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '05/12',
    time: '23:12',
    size: '9.64MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T23:12:00')
  },
  {
    id: '3',
    filename: 'Screens...09-29',
    duration: '24:12',
    date: '05/12',
    time: '07:56',
    size: '10.22MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T07:56:00')
  },
  {
    id: '4',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '05/12',
    time: '07:44',
    size: '9.92MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T07:44:00')
  },
  {
    id: '5',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '05/12',
    time: '06:56',
    size: '10.11MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T06:56:00')
  },
  {
    id: '6',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '05/12',
    time: '06:33',
    size: '4.22MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2024-05-12T06:33:00')
  },
  {
    id: '7',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '2022/05/12',
    time: '11:45',
    size: '11.45MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2022-05-12T11:45:00')
  },
  {
    id: '8',
    filename: 'Screens...09-29',
    duration: '23:19',
    date: '2022/05/12',
    time: '45.78',
    size: '45.78MB',
    thumbnail: '/api/placeholder/120/80',
    fullDate: new Date('2022-05-12T12:00:00')
  }
]

export default function VideoList() {
  const [videoData] = useState<VideoItem[]>(mockVideoData)

  // 按日期分组视频数据
  const groupedVideos = useMemo(() => {
    const groups: { [key: string]: VideoItem[] } = {}
    const today = new Date()
    const todayStr = today.toDateString()

    videoData.forEach(video => {
      const videoDateStr = video.fullDate.toDateString()
      let groupKey: string

      if (videoDateStr === todayStr) {
        groupKey = '今天'
      } else {
        // 格式化为 MM/DD 或 YYYY/MM/DD
        const year = video.fullDate.getFullYear()
        const month = String(video.fullDate.getMonth() + 1).padStart(2, '0')
        const day = String(video.fullDate.getDate()).padStart(2, '0')

        if (year === today.getFullYear()) {
          groupKey = `${month}/${day}`
        } else {
          groupKey = `${year}/${month}/${day}`
        }
      }

      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(video)
    })

    return groups
  }, [videoData])

  const handleBack = () => {
    // 返回逻辑
    console.log('返回')
  }

  const handleSettings = () => {
    // 设置逻辑
    console.log('设置')
  }

  const renderVideoItem = (video: VideoItem) => (
    <div key={video.id} className={styles.videoItem}>
      <div className={styles.thumbnailContainer}>
        <div className={styles.thumbnail}>
          {/* 使用灰色背景模拟视频缩略图 */}
          <div className={styles.thumbnailPlaceholder}>
            <div className={styles.playIcon}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.8)"/>
                <polygon points="10,8 16,12 10,16" fill="#333"/>
              </svg>
            </div>
          </div>
          <div className={styles.duration}>
            {video.duration}
          </div>
        </div>
      </div>
      <div className={styles.videoInfo}>
        <div className={styles.filename}>{video.filename}</div>
        <div className={styles.metadata}>
          <span className={styles.datetime}>{video.date} {video.time}</span>
          <span className={styles.size}>{video.size}</span>
        </div>
      </div>
    </div>
  )

  return (
    <div className={styles.container}>
      <NavigatorBar
        title="视频"
        onBack={handleBack}
        right={
          <div className={styles.settingsIcon} onClick={handleSettings}>
            <div className={styles.settingsButton}>
              <div className={styles.settingsDot}></div>
              <div className={styles.settingsDot}></div>
            </div>
          </div>
        }
      />

      <div className={styles.content}>
        {Object.entries(groupedVideos).map(([groupKey, videos]) => (
          <div key={groupKey} className={styles.videoGroup}>
            <div className={styles.groupHeader}>
              <span className={styles.groupTitle}>{groupKey}</span>
              <span className={styles.groupCount}>| {videos.length}项</span>
            </div>

            <div className={styles.videoGrid}>
              {videos.map(renderVideoItem)}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
