

//用来存储方法,APP调用
window.hs_APPtoWebMethodMap = new Map()


//=================================js调用APP方法实现===============================================

window.hs_requestId = 0
window.hs_RequestIdMap = new Map()

// js调APP方法 （参数分别为:app提供的方法名  传给app的数据  回调）
async function hs_callHandler(name, data, callback) {

    if (window.flutter_inappwebview){

      window.hs_requestId ++
      var requestIdStr = `${window.hs_requestId}`
      window.hs_RequestIdMap.set(requestIdStr,callback)

      var code = await window.flutter_inappwebview.callHandler("hs_webCallAppHandler",name, data,requestIdStr);
       console.log(`code:${code}`);
      if (code == 0){
        //调用成功
       // console.debug(`***web测试 ${requestIdStr} web调用app方法成功 ${name}`)
      }else {
        //调用失败
       // console.debug(`***web测试 ${requestIdStr} web调用app方法失败,code:${code}`)
        window.hs_RequestIdMap.delete(requestIdStr)
        if (callback){
            var response = {
              code:code,
              msg:"方法调用失败"
            }
          callback( JSON.stringify(response))
        }
      }
    }
    else{
       //console.log('flutter_inappwebview 不存在');
        if (callback){
            var response = {
              code:-9999,
              msg:"flutter_inappwebview 未初始化"
            }
          callback( JSON.stringify(response))
        }
    }

}

function hs_appCallBackToWeb(response,requestId){
 console.debug(`***web测试 收到app回调 ${response}, requestId:${requestId}`)
    if ( window.hs_RequestIdMap.get(requestId)){
    // console.debug(`***web测试 ${requestId} APP返回数据成功 ${response}`)
       var callback =  window.hs_RequestIdMap.get(requestId)
       if (callback){
         //console.log(`***web测试 中间层返回成功。`)
         callback(response)
         window.hs_RequestIdMap.delete(requestId)
       }
       else {
       // console.debug(`***web测试 中间层返回失败`)
       }
    }
    else {
        //console.debug(`***web测试 ${requestId} 方法没注册`)
    }

}



//=================================APP调用js方法实现===============================================


// APP调js方法 （参数分别为:js提供的方法名  回调）
function hs_registerHandler(name, callback) {

    //console.debug(`***web测试 web注册方法:${name}, window.hs_APPtoWebMethodMap:${window.hs_APPtoWebMethodMap},${hs_APPtoWebMethodMap}`)
    window.hs_APPtoWebMethodMap.set(name,callback)
    //console.debug(`***web测试 web方法:${name} 注册成功`)

}

function hs_AppCallWeb(funcName,paramsJsonStr,requestId){
   //console.debug(`***web测试 web收到APP调用的方法:${funcName}`)
   if( window.hs_APPtoWebMethodMap.get(funcName)){
   // console.debug(`***web测试 该方法有注册`)

      var callback =  window.hs_APPtoWebMethodMap.get(funcName)
      callback(paramsJsonStr,(webResponseJson)=>{
              if (window.flutter_inappwebview){
                 window.flutter_inappwebview.callHandler("hs_callBackId",requestId,webResponseJson)
              }
      })
   }
   else {
   if (window.flutter_inappwebview){
   var response = {
              code:-9998,
              msg:"该方法没有注册",
            }
                 window.flutter_inappwebview.callHandler("hs_callBackId",requestId,JSON.stringify(response))
          }
   }
}
