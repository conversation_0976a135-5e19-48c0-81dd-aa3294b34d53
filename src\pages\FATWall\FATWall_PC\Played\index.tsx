import styles from '../All/index.module.scss';
import baseStyles from '../Recently/RecentlyPlay/index.module.scss';
import { PreloadImage } from '@/components/Image';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import refreshIcon from '@/Resources/layout/refreshBtn.png';
import refreshIcon_dark from '@/Resources/layout/refreshBtn_dark.png';
import dataSelect from '@/Resources/player/dateSelect.png';
import FilmFilter from '../../../../components/FATWall_APP/FilmFilter';
import arrow from '@/Resources/layout/arrow.png';
import arrow_dark from '@/Resources/layout/arrow_dark.png';
import { useTheme } from '@/utils/themeDetector';
import FilterFilmCard from '../../../../components/FATWall_APP/FilterFilmCard';
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import { px2rem } from '@/utils/setRootFontSize';
import searchIcon from '@/Resources/filmWall/search.png';
import { modalShow } from '@/components/List';
import MatchCorrection from '../All/MatchCorrection';
import { defaultFiltersByPc, filterItemType, filterTypeList } from '@/components/FATWall_APP/FATWALL_CONST';
import { handleFilter } from '../../FATWall_APP/All';
import { collect, getFilePath, getMediaListFromLib, markWatched, mediaDelete, mediaProps, move2trashbin } from '@/api/fatWall';
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { defaultTypeList, selectFilmProps } from '../All';
import FATErrorComponents from '../Error';
import { Toast } from '@/components/Toast/manager';
import { useHistory } from 'react-router-dom';
import { defaultPageParam } from '../../FATWall_APP/Recently';
import { useLibraryList } from '..';


const PlayedByDesktop = () => {
  const history = useHistory();
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByPc);
  const [collapse, setCollapse] = useState<boolean>(false);
  const { isDarkMode } = useTheme();
  const [hoverKey, setHoverKey] = useState<string>(''); // 悬浮key
  const [selectList, setSelectList] = useState<selectFilmProps[]>([]); // 已选择list
  const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);

  const { runAsync: getMediasFromLib } = useRequest(getMediaListFromLib, { manual: true }); // 根据媒体库获取所有资源
  const { runAsync: collectFilmRun } = useRequest(collect, { manual: true }); // 收藏或取消收藏
  const { runAsync: markWatchedRun } = useRequest(markWatched, { manual: true }); // 标记已观看或未观看
  const { runAsync: m2tRun } = useRequest(move2trashbin, { manual: true }); // 删除文件
  const { runAsync: deleteRun } = useRequest(mediaDelete, { manual: true }); // 从媒体库移除

  const [medias, setMedias] = useState<mediaProps[]>([]);
  const [filterItem, setFilterItem] = useState<filterItemType>({ sort_type: 0, asc: 0 });
  const filterItemRef = useRef<filterItemType>({ sort_type: 0, asc: 0 });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByPc); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const libs = useLibraryList().libs;

  // 媒体库数据
  const filterFilm = useMemo(() => {
    return medias.map((item) => {
      return { ...item, label: item.trans_name, score: item.score || 0, time: `${item.year}`, cover: item.poster.length > 0 ? item.poster[0] : '', isLike: item.favourite, }
    })
  }, [medias])

  const initMediaInfo = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    const mediaRes = await getMediasFromLib({ lib_id: 0, filter: { ...pageOptRef.current, ...filterItemRef.current, ...filter, seen: 1 } }).catch((e) => console.log('获取媒体库影视列表失败：', e));
    if (mediaRes && mediaRes.code === 0 && mediaRes.data) {
      if (mediaRes.data.count < pageOptRef.current.limit) setHasMore(false);
      callback(mediaRes.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [getMediasFromLib])

  // 初始化数据
  useEffect(() => {
    initMediaInfo((data) => setMedias(data), handleFilter(defaultFiltersByPc));
  }, [initMediaInfo])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      initMediaInfo((data) => setMedias(p => [...p, ...data]), handleFilter(filters));
    }
  }, [inViewport, initMediaInfo])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilter(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(true);
    pageOptRef.current = defaultPageParam; // 重置页数
    initMediaInfo((data) => setMedias(data), filter);
  }, [initMediaInfo])

  useUpdateEffect(() => {
    filterItemRef.current = { ...filterItemRef.current, sort_type: filterItem.sort_type, asc: filterItem.asc }; // 记录之前的筛选条件，用于判断筛选条件是否变化
    initMediaInfo((data) => setMedias(data), handleFilter(filters));
  }, [filterItem])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    getDataByFilters(filters);
  }, [filters, getDataByFilters])

  // 选择回调
  const selectCallback = useCallback((type: string, key: string) => {
    setFilters((filter: any) => {
      let f = { ...filter };
      f[type] = key;
      return f;
    })
  }, [])

  const clearAndRefresh = useCallback(() => {
    setHoverKey('');
    setSelectList([]);
    setFilters(defaultFiltersByPc); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByPc)) {
        getDataByFilters(defaultFiltersByPc);
      }
    }
  }, [getDataByFilters])

  // 选中
  const clickCallback = useCallback((it: any) => {
    setSelectList((p: any[]) => {
      let pr = [...p];
      const isIncludes: boolean = pr.includes(it);
      if (isIncludes) {
        pr = pr.filter(item => item !== it);
      } else {
        pr.push(it);
      }
      return pr;
    })
  }, [])

  // 收藏
  const like = useCallback(async () => {
    const res = await collectFilmRun({ media_ids: selectList.map(it => it.media_id), favourite: 1 });
    if (res && res.code === 0) {
      Toast.show('收藏成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('收藏失败，请稍后再试！');
  }, [collectFilmRun, clearAndRefresh, selectList])

  // 取消收藏
  const cancelLike = useCallback(async () => {
    const res = await collectFilmRun({ media_ids: selectList.map(it => it.media_id), favourite: 0 });
    if (res && res.code === 0) {
      Toast.show('取消收藏成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('取消收藏失败，请稍后再试！');
  }, [collectFilmRun, clearAndRefresh, selectList])

  // 取消标记
  const cancelPlayed = useCallback(async () => {
    const res = await markWatchedRun({ media_ids: selectList.map(it => it.media_id), seen: 0 });
    if (res && res.code === 0) {
      Toast.show('取消标记成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('标记失败，请稍后再试！');
  }, [markWatchedRun, clearAndRefresh, selectList])

  // 标记
  const played = useCallback(async () => {
    const res = await markWatchedRun({ media_ids: selectList.map(it => it.media_id), seen: 1 });
    if (res && res.code === 0) {
      Toast.show('标记成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('标记失败，请稍后再试！');
  }, [markWatchedRun, clearAndRefresh, selectList])

  // 修正
  const edit = useCallback(() => {
    setShowMatchCorrection(true)
    console.log('修改成功!')
  }, [])

  const move2Trashbin = useCallback(async (m) => {
    const mediaIds = selectList.map(it => it.media_id);
    const res = await getFilePath({ media_ids: mediaIds, lib_id: 0 });
    if (res && res.code === 0) {
      const res1 = await m2tRun({ path: res.data.path });
      if (res1 && res1.code === 0) {
        Toast.show('删除成功!');
        setSelectList([]);
        m.destroy();
        clearAndRefresh();

        return;
      }
      Toast.show('删除失败，请稍后再试！');
    }
  }, [m2tRun, clearAndRefresh, selectList])

  const delFile = useCallback((modal) => {
    modalShow(`是否确定删除${selectList.length}个文件？`, <>删除的文件将移至“回收站”，保留30天</>, async (m) => {
      const res = await deleteRun({ media_ids: selectList.map(it => it.media_id), lib_id: 0 });
      if (res && res.code === 0) {
        Toast.show('删除成功!');
        setSelectList([]);
        m.destroy();
        modal.destroy();
        clearAndRefresh();

        return;
      }
      Toast.show('删除失败!');
    }, () => null, false, { position: 'center', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } })
  }, [deleteRun, clearAndRefresh, selectList])

  // 删除
  const del = useCallback(() => {
    const m = modalShow('确认删除吗？', (
      <>
        <div className={styles.modal_button} onClick={() => delFile(m)}>仅从媒体库移除</div>
        <div className={styles.modal_button} onClick={() => move2Trashbin(m)}>删除文件</div>
      </>
    ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'center' })
  }, [delFile, move2Trashbin])

  // 搜索
  const search = useCallback(() => {

  }, [])
  const toDarmaOrMovie = useCallback((item: any) => {
    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });
    
    history.push(`/filmAndTelevisionWall_pc/all/videoDetails?${params.toString()}`);
  }, [history])

  useEffect(() => {
    if (isError || medias.length === 0) {
      setCollapse(true); // 如果没有影片数据，则默认收起筛选条件
    }

    if (medias.length > 0 && pageOptRef.current.offset === 0) {
      setCollapse(false); // 如果有影片数据，则默认展开筛选条件
    }
  }, [isError, medias.length])

  return (
    <div className={styles.container}>
      <div className={`${baseStyles.header} ${styles.header}`}>
        <div className={baseStyles.util_items} style={{ justifyContent: 'flex-start' }}>
          {
            defaultTypeList.map(it => (
              <div key={it.key} className={`${styles.tabs_item} ${filters['classes'] === it.key ? styles.selected : ''}`} onClick={() => selectCallback('classes', it.key)}>
                <span>{it.title}</span>
              </div>
            ))
          }
        </div>
        <div className={baseStyles.util_items}>
          {
            selectList.length === 0 ? (
              <>
                <PreloadImage src={searchIcon} alt="search" onClick={search} />
                <div className={baseStyles.operationItem} onClick={() => setCollapse((c: boolean) => !c)}>
                  <span>筛选</span>
                  <PreloadImage style={{ transform: !collapse ? 'rotate(0deg)' : 'rotate(180deg)' }} src={isDarkMode ? arrow_dark : arrow} alt='more' />
                </div>
                <div style={{ opacity: filterItem.sort_type ? '0.3' : '1' }} className={baseStyles.operationItem} onClick={() => setFilterItem(prev => { return { ...prev, sort_type: 0, asc: prev.sort_type === 0 ? prev.asc ? 0 : 1 : prev.asc } })}>
                  <span>添加时间</span>
                  <PreloadImage src={dataSelect} alt='select' />
                </div>
                <div style={{ opacity: filterItem.sort_type ? '1' : '0.3' }} className={baseStyles.operationItem} onClick={() => setFilterItem(prev => { return { ...prev, sort_type: 1, asc: prev.sort_type === 1 ? prev.asc ? 0 : 1 : prev.asc } })}>
                  <span>评分</span>
                  <PreloadImage src={dataSelect} alt='select' />
                </div>
                <PreloadImage src={isDarkMode ? refreshIcon_dark : refreshIcon} alt="refresh" onClick={clearAndRefresh} />
              </>
            ) : (
              <>
                {
                  selectList.every((it) => it.isLike) ? (
                    <div className={baseStyles.operationItem} onClick={cancelLike}>
                      <span>取消收藏</span>
                    </div>
                  ) : (
                    <div className={baseStyles.operationItem} onClick={like}>
                      <span>收藏</span>
                    </div>
                  )
                }
                {
                  selectList.every((it) => it.seen) ? (
                    <div className={baseStyles.operationItem} onClick={cancelPlayed}>
                      <span>取消已观看</span>
                    </div>
                  ) : (
                    <div className={baseStyles.operationItem} onClick={played}>
                      <span>已观看</span>
                    </div>
                  )
                }
                <div className={baseStyles.operationItem} onClick={edit}>
                  <span>修正匹配信息</span>
                </div>
                <div className={baseStyles.operationItem} style={{ color: 'rgba(255, 112, 113, 1)' }} onClick={del}>
                  <span>删除</span>
                </div>
              </>
            )
          }
        </div>
      </div>

      <div className={`${styles.content}`}>

        {/* 条件筛选 */}
        <FilmFilter filters={filters} filterTypeList={filterTypeList} controlFilters={setFilters} value={collapse} onChange={setCollapse} type='pc' isDisabled={selectList.length !== 0} />
        <FATErrorComponents span={isError ? '获取失败' : '暂无内容'} canTry={isError} refresh={clearAndRefresh} show={isError || libs.length === 0 || filterFilm.length === 0} subSpan={libs.length > 0 && filterFilm.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>

          {/* 影片渲染 */}
          <div className={styles.filter_films_container} style={{ marginTop: collapse ? 0 : px2rem('15px') }}>
            {
              filterFilm.map((item, index) => (
                <div key={item.label + index} className={styles.film_card_container} style={{ background: selectList.includes(item) ? 'var(--fat-card-hover-bg)' : '' }} onMouseLeave={() => setHoverKey('')} onMouseEnter={() => setHoverKey(item.label)} onClick={() => toDarmaOrMovie(item)}>
                  <FilterFilmCard title={item.label} subtitle={item.time} score={item.score} cover={item.cover} isLike={item.isLike ? true : false} />
                  <div className={baseStyles.select_item} onClick={(e) => { e.stopPropagation(); clickCallback(item); }} style={{ visibility: (selectList.includes(item) || hoverKey === item.label) ? 'visible' : 'hidden' }}>
                    {selectList.includes(item) ? <PreloadImage src={selected} alt="selected" /> : <PreloadImage src={notSelect} alt="notSelect" />}
                  </div>
                </div>
              ))
            }
          </div>

          {
            hasMore && filterFilm.length >= pageOptRef.current.limit && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
          }
        </FATErrorComponents>
      </div>
      <MatchCorrection
        visible={showMatchCorrection}
        onClose={() => {
          setShowMatchCorrection(false);
          setSelectList([]);
        }}
        selectList={selectList}
        refresh={clearAndRefresh}
      />
    </div>
  )
}

export default PlayedByDesktop;