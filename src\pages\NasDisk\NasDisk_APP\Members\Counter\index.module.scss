// 收银台覆盖层
.counterOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: slideInFromBottom 0.3s ease-out;

  @keyframes slideInFromBottom {
    0% {
      transform: translateY(100%);
    }
    100% {
      transform: translateY(0);
    }
  }

  // 收银台头部
  .counterHeader {
    height: 56px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .counterTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      text-align: center;
      flex: 1;
    }

    .counterClose {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e8e8e8;
        color: #333;
      }

      &:active {
        background-color: #d9d9d9;
      }
    }
  }

  // 收银台容器
  .counterContainer {
    flex: 1;
    position: relative;
    background-color: #fff;
    overflow: hidden;

    .counterIframe {
      width: 100%;
      height: 100%;
      border: none;
    }

    // 加载状态覆盖层
    .loadingOverlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;

      .loadingSpinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #34BBBF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      .loadingText {
        font-size: 16px;
        color: #666;
        text-align: center;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  }
}
