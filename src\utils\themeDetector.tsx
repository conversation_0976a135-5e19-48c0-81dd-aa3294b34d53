import React, { useEffect, createContext, useContext, useState } from 'react';
import themes from '../utils/theme';

// 创建Context
type ThemeContextType = {
  isDarkMode: boolean;
};

const ThemeContext = createContext<ThemeContextType>({
  isDarkMode: false,
});

export const useTheme = () => useContext(ThemeContext);

const ThemeDetector: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] =  useState(
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  const applyTheme = (theme: 'light' | 'dark') => {
    const root = document.documentElement;
    const themeVariables = themes[theme];
    for (const [key, value] of Object.entries(themeVariables)) {
      root.style.setProperty(key, value);
    }
    setIsDarkMode(theme === 'dark');
  };

  useEffect(() => {
    // 检测系统是否处于暗黑模式
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    applyTheme(isDarkMode ? 'dark' : 'light');

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      applyTheme(e.matches ? 'dark' : 'light');
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return (
    <ThemeContext.Provider value={{ isDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeDetector;