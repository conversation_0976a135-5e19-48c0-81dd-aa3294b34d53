import { use<PERSON><PERSON><PERSON><PERSON>idth } from "@/components/CameraPlayer/utils/utils";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { px2rem } from "@/utils/setRootFontSize";
import { IDevicePlugin } from "@/components/CameraPlayer/components/plugin/DevicePlugin";
import MonitorPlayer, { IDualOptions, PlayerType } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import { getVideoRecord, startLiveWithCamera } from "@/api/ipc";
import styles from "../index.module.scss";
import { ICameraDetail, IEventVideo } from "../../IPC_APP/CameraDetail";
import { endOfDay, startOfDay } from "date-fns";
import SupportInformation from "../SupportInformation";
import { useRequest, useUpdateEffect } from 'ahooks';
import { needOpenCamera } from "@/api/cameraPlayer";
import { useCameras, useOpenCameraModal } from "..";

export interface ICamera {
  url: string,
  deviceOptions: IDevicePlugin
  type: PlayerType,
  dualOptions?: IDualOptions
}

interface InitCameraProps {
  cameras: (ICameraDetail & ICamera)[];
  setCameras: (camera: (ICameraDetail & ICamera)[]) => void;
}

const InitCamera = (props: InitCameraProps) => {
  const [isFull, setIsFull] = useState<boolean>(false);
  const { cameras, setCameras } = props;
  const [eventData, setEventData] = useState<IEventVideo[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date()); // 当前时间
  const pageRef = useRef<{ size: number, token: string }>({ size: 0, token: '' }); // size为0的时候获取全部
  const [showSupportInfo, setShowSupportInfo] = useState<boolean>(false);

  // 开启直播
  const { runAsync } = useRequest(startLiveWithCamera, { manual: true });

  // 根据宽度调整播放器大小
  const { containerRef, width, cancelResizeObserver, forcePause } = useContainerWidth({
    debounce: 2000,
    pause: isFull
  });

  const camera_lens = useMemo(() => {
    let result: string[] = [];
    cameras.forEach((it) => {
      it.key_frame.forEach((item) => result.push(`${it.did}_${item.lens_id}`));
    });
    return result;
  }, [cameras])

  // 获取当日事件数据
  const getTodayEventData = useCallback(async () => {
    if (camera_lens.length === 0) return; // 如果没有镜头则不执行查询
    
    const params = {
      page: pageRef.current,
      options: {
        option: ['time', 'camera_lens'],
        // event_name: t,
        camera_lens: camera_lens,
        time: {
          start: startOfDay(selectedDate).getTime(),
          end: endOfDay(selectedDate).getTime()
        }
      }
    }
    console.log('开始查询事件,当前查询镜头为:', camera_lens, '总Params:', params);
    const res = await getVideoRecord(params).then().catch(() => null);

    if (res && res.code === 0 && res.data) {
      // if (res.data.videos.length >= pageRef.current.size) setHasMore(true);
      setEventData((p: IEventVideo[]) => {
        return [...p, ...res.data.videos.map((it, index) => { return { ...it, id: it.camera_lens + index } })]
      });
      pageRef.current = { ...pageRef.current, token: res.data.page.token }
    }
  }, [camera_lens, selectedDate])

  useUpdateEffect(() => {
    getTodayEventData();
  }, [getTodayEventData])

  useEffect(() => {
    return () => {
      cancelResizeObserver();
    };
  }, [cancelResizeObserver]);

  const { open } = useOpenCameraModal();

  const checkIsNeedOpenCameraModal = useCallback(async () => {
    if (cameras.length === 0) return; // 摄像机列表为空时不执行检查逻辑

    console.log('开始查询是否需要打开摄像机modal...');
    await needOpenCamera(res => {
      if (res && res.data && res.data.camera) {
        console.log('需要打开摄像机modal:', res.data.camera.did);
        open(res.data.camera.did);
      }
    }).catch((e) => console.log(e));
  }, [cameras.length, open])

  useEffect(() => {
    checkIsNeedOpenCameraModal();
  }, [checkIsNeedOpenCameraModal])

  useEffect(() => {
    const handler = () => {
      const isFull = !!document.fullscreenElement;
      if (isFull) forcePause();
    };

    document.addEventListener("fullscreenchange", handler);
    return () => document.removeEventListener("fullscreenchange", handler);
  }, [forcePause]);

  // 摄像机获取对应信息
  const cameraObj = useCameras();
  const getList = useCallback(async () => {
    const cameraList: (ICameraDetail & ICamera)[] = [];
    const result: string[] = [];
    try {
      // 获得开启直播的镜头id
      cameraObj.cameras.forEach((it: ICameraDetail) => {
        if (!it.isOnline) return;
        it.key_frame.forEach((its) => {
          result.push(`${it.did}_${its.lens_id}`);
        })
      })

      // 镜头id的数组长度不能为0
      if (result.length !== 0) {
        const resp = await runAsync(result);
        // 开始处理每一个摄像机所需属性
        cameraObj.cameras.forEach((it: ICameraDetail) => {
          // 设备信息
          const deviceOptions: IDevicePlugin = {
            title: it.name,
            subtitle: it.model,
            titleColor: "rgba(255,255,255,0.8)",
            subtitleColor: "rgba(255,255,255,0.5)",
          }

          if (!resp.data[`${it.did}_0`]) {
            cameraList.push({ ...it, type: 'Live', url: '', deviceOptions: deviceOptions });
            return; // 如果该id不是开启直播中的摄像头则不处理多摄
          }
          // 多摄选项
          const dualOptions: IDualOptions | undefined = it.key_frame.length > 0 ? {
            urls: {
              main: resp.data[`${it.did}_0`].hls_file,
              secondary: resp.data[`${it.did}_1`].hls_file,
              third: it.key_frame[2] ? resp.data[`${it.did}_2`].hls_file : undefined
            },
            poster: {
              main: resp.data[`${it.did}_1`].key_frame[0].frame,
              secondary: resp.data[`${it.did}_0`].key_frame[0].frame,
              third: it.key_frame[2] ? resp.data[`${it.did}_2`].key_frame[0].frame : undefined
            },
            psm: {
              main: it.key_frame[0].psm,
              secondary: it.key_frame[1].psm,
              third: it.key_frame[2] ? it.key_frame[2].psm : undefined
            },
          } : undefined;
          cameraList.push({ ...it, type: 'Live', deviceOptions: deviceOptions, url: resp.data[`${it.did}_0`].hls_file, dualOptions: dualOptions });
        })
      } else {
        // 如果没有开启直播的镜头，则只显示摄像机列表
        cameraObj.cameras.forEach((it: ICameraDetail) => {
          const deviceOptions: IDevicePlugin = {
            title: it.name,
            subtitle: it.model,
            titleColor: "rgba(255,255,255,0.8)",
            subtitleColor: "rgba(255,255,255,0.5)",
          }
          cameraList.push({ ...it, type: 'Live', url: '', deviceOptions: deviceOptions });
        });
      }

      setCameras(cameraList);
    } catch (e) {
      console.log('err', e);
    }
  }, [cameraObj.cameras, runAsync, setCameras])

  useEffect(() => {
    getList();
  }, [getList])

  const cameraWidth = useMemo(() => {
    return (width / 2).toFixed(1);
  }, [width])

  return (
    <>
      <div className={styles.content} ref={containerRef}>
        {
          cameras.map((camera) => {
            return (
              <div key={camera.name} className={styles.monitorPlayer} style={{ width: px2rem(`${cameraWidth}px`) }}>
                <MonitorPlayer selectedDate={selectedDate} setSelectedDate={setSelectedDate} fullScreenCallback={setIsFull} baseConfig={{ width: `${(Number(cameraWidth) - 6)}px`, url: camera.url, type: camera.type, mediaName: camera.did }}
                  key={camera.did} isOnline={camera.isOnline} deviceOptions={camera.deviceOptions} dualOptions={camera.dualOptions} isDashboard initPlayStatus={false} cameraDetail={camera} eventData={eventData} poster={camera.dualOptions?.poster.main} />
              </div>
            )
          })
        }
      </div>
      <div className={styles.footer}>
        <span onClick={() => setShowSupportInfo(true)}>哪些摄像机支持添加到小米NAS？</span>
      </div>
      {/* 支持的摄像机弹窗 */}
      {
        showSupportInfo && (
          <SupportInformation
            visible={true}
            onClose={() => setShowSupportInfo(false)}
          />
        )
      }
    </>
  )
}

export default InitCamera;