import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
// import rise from "@/Resources/icon/rise.png";
// import down from "@/Resources/icon/down.png";
import { <PERSON><PERSON>, Divider } from "antd";
import List, { IListData, modalShow } from "@/components/List";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTheme } from "@/utils/themeDetector";
import themes from "@/utils/theme";
import { px2rem } from "@/utils/setRootFontSize";
import DeviceRecordConfig, { IDeviceRecordConfig } from "./DeviceRecordConfig";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { ICollapsePanel } from "@/layouts/Layout";
import { cleanRecord, getRecordConfig, removeRecordCamera, renameDevice, setupRecordCamera } from "@/api/ipc";
import { useRequest } from 'ahooks';

export interface IDeviceDetail {
  key: string;
  position: string;
  name: string;
  icon: string;
  camera_lens: string[];
  data: {
    deviceConfig: {
      isRecording: boolean;
      remark: string;
      ip: string;
      mac: string;
      recordTime: number;
      size: number;
      totalSize: number;
      // rise: string;
      // down: string;
    };
    recordConfig: IDeviceRecordConfig
  }
}

interface IDesktopDeviceDetail {
  item?: (ICollapsePanel & ICameraDetail)
}

interface IDestroy {
  onDestroy: () => void;
}

const IPCDesktopDeviceDetail = (props: IDesktopDeviceDetail & IDestroy) => {
  const { onDestroy, item } = props;
  const { isDarkMode } = useTheme();
  const [curDevice, setCurDevice] = useState<(ICollapsePanel & ICameraDetail & IDeviceDetail) | undefined>(undefined);

  // 请求ipc本地录制配置
  const { runAsync: recordConfigRunAsync } = useRequest(getRecordConfig, { manual: true })

  // 备注属性接口hook
  const renameDeviceRes = useRequest(renameDevice, {
    manual: true,
    onSuccess(data, params) {
      if (data && data.code === 0) {
        console.log('修改成功，设备新备注为：', params[0].name);
        setCurDevice((prev: any) => {
          let p = { ...prev };
          p!.data.deviceConfig.remark = params[0].name;
          return p;
        })
      }
    },
    onError(err) {
      console.log('修改失败:', err);
    }
  });

  // 设置IPC本地录制属性hook
  const setRecordRes = useRequest(setupRecordCamera, {
    manual: true,
    onSuccess(data, params) {
      if (data && data.code === 0) {
        setCurDevice((prev: any) => {
          let p = { ...prev };
          p!.data.deviceConfig.isRecording = params[0].config.record_enabled;
          return p;
        })
        console.log('修改成功，当前设置的属性为：', params[0].config.record_enabled);
      }
    },
    onError(err) {
      console.log('修改失败', err);
    }
  });

  // 删除设备hook
  const delDeviceRes = useRequest(removeRecordCamera, { manual: true });

  // 清空录制配置hook
  const clearRecordRes = useRequest(cleanRecord, { manual: true });

  // 是否录制属性改变回调
  const recordingCallback = useCallback((checked) => {
    if (!item) return;
    setRecordRes.run({ camera: [item.did], config: { record_enabled: checked } });
  }, [item, setRecordRes])

  // 备注属性改变回调
  const remarkCallback = useCallback((value) => {
    if (!item) return;
    renameDeviceRes.run({ name: value, did: item.did });
  }, [item, renameDeviceRes])

  const getInfo = useCallback(async () => {
    if (!item) return;
    const recordConfigRes = await recordConfigRunAsync({ camera: item.did });
    if (recordConfigRes && recordConfigRes.code === 0 && recordConfigRes.data) {
      const rData = recordConfigRes.data;
      const config = {
        ...item,
        position: item.name,
        camera_lens: item.key_frame.map((it) => `${item.did}_${it.lens_id}`),
        data: {
          deviceConfig: {
            ip: item.ip,
            mac: item.mac,
            totalSize: item.space_limit,
            size: item.used_space,
            isRecording: rData.record_enabled,
            recordTime: item.record_period,
            remark: item.name,
            rise: "0",
            down: "0"
          },
          recordConfig: {
            saveURl: rData.path || "",
            isDynamicBackup: rData.backup,
            videoDuration: rData.retention_period,
            videoSize: rData.space_limit,
            capacityShortageHandlingStrategy: rData.space_limit_policy,
            capacityWarningThreshold: '95%', // 容量阈值
            recordPlan: rData.record_schedule,
            recordModel: rData.record_mode,
            source: rData.event_source,
            eventBeforeDuration: rData.record_bf_event,
            eventAfterDuration: rData.record_af_event,
            checkEvent: rData.event_trigger || [],
            internetTimeServer: rData.ntp_source
          }
        }
      }
      setCurDevice(config);
      console.log('查询成功,摄像头录制配置:', config);
    }
  }, [item, recordConfigRunAsync])

  useEffect(() => {
    getInfo();
  }, [getInfo])

  const dataSource: IListData[] = useMemo(() => {
    return [
      { key: 'isRecording', type: 'switch', label: '开启录制', value: curDevice?.data.deviceConfig.isRecording, onCallback: recordingCallback },
    ]
  }, [curDevice?.data.deviceConfig.isRecording, recordingCallback])

  const deviceDataSource: IListData[] = useMemo(() => {
    // 录制时长转化
    const recordTimeTransform = (time: number | undefined) => {
      if (!time) return;
      const days = Math.floor(time / 1440);
      const remaining = time % 1440;
      const hours = Math.floor(remaining / 60);
      const mins = remaining % 60;
      if (days === 0) {
        if (hours === 0) {
          return `${mins}分钟`;
        }
        return `${hours}小时${mins}分钟`;
      }
      return `${days}天${hours}小时${mins}分钟`;
    }
    // 录制空间转化
    const storageSpaceTransform = (size: number | undefined, totalSize: number | undefined) => {
      if (!size || !totalSize) return;
      return `${Math.floor(size / 1024).toFixed(2)}GB/${Math.floor(totalSize / 1024).toFixed(2)}GB`;
    }

    const recordTime = recordTimeTransform(curDevice?.data.deviceConfig.recordTime);
    const storageSpace = storageSpaceTransform(curDevice?.data.deviceConfig.size, curDevice?.data.deviceConfig.totalSize);

    return [
      {
        key: 'remark', type: 'input', label: '备注', value: curDevice?.data.deviceConfig.remark, onCallback: remarkCallback, options: {
          inputOptions: {
            inputRules: [{
              required: true,
              message: '请输入备注！'
            }, () => ({
              validator(_, value) {
                if (value.length > 40) {
                  return Promise.reject(new Error('名字太长，请重新输入！'));
                }
                return Promise.resolve();
              }
            })]
          }
        }
      },
      { key: 'ip', type: 'text', label: 'IP地址', value: curDevice?.data.deviceConfig.ip },
      { key: 'mac', type: 'text', label: 'MAC地址', value: curDevice?.data.deviceConfig.mac },
      { key: 'recordTime', type: 'text', label: '已录制时长', value: recordTime },
      {
        key: 'storageSpace', type: 'text', label: '占用录制空间', value: storageSpace,
        options: { textColor: isDarkMode ? themes.light['--emergency-text-color'] : themes.dark['--emergency-text-color'] }
      },
    ]
  }, [curDevice?.data.deviceConfig.remark, curDevice?.data.deviceConfig.ip, curDevice?.data.deviceConfig.mac, curDevice?.data.deviceConfig.recordTime, curDevice?.data.deviceConfig.size, curDevice?.data.deviceConfig.totalSize, remarkCallback, isDarkMode])

  const recordSource: IListData[] = useMemo(() => {
    return [
      {
        key: 'recordConfig', type: 'modal', label: '录制配置', options: {
          modalOptions: {
            content: <DeviceRecordConfig cameraId={curDevice?.did} curDevice={curDevice} setCurDevice={setCurDevice} onDestroy={onDestroy} />,
            footer: null,
            title: '录制配置',
            contentStyle: { width: px2rem("550px"), height: px2rem("587px") }
          }
        }
      },
    ]
  }, [curDevice, onDestroy])

  //清除
  const onClear = useCallback(() => {
    if (!item) return;
    modalShow('清空存档', '清空后无法恢复,是否确认清空?', async (m) => {
      try {
        const res = await clearRecordRes.runAsync({ camera: [item.did] });
        if (res && res.code === 0) {
          console.log('存档已清空!');
          onDestroy();
          m.destroy();
        }
      } catch (e) {
        console.log(e);
      }
    }, () => null, false, { okBtnText: '清空', okBtnStyle: { backgroundColor: "rgba(0, 0, 0, 0.06)", color: "rgba(255, 0, 0, 1)" } })
  }, [clearRecordRes, item, onDestroy])

  //删除
  const onDelete = useCallback(() => {
    if (!item) return;
    modalShow('是否确认删除设备', '', async (m) => {
      try {
        const res = await delDeviceRes.runAsync({ camera: [item.did] });
        if (res && res.code === 0) {
          console.log('设备已删除!', res.result);
          onDestroy();
          m.destroy();
        }
      } catch (e) {
        console.log(e);
      }
    }, () => null, false, { okBtnText: '删除', okBtnStyle: { backgroundColor: "rgba(0, 0, 0, 0.06)", color: "rgba(255, 0, 0, 1)" } })
  }, [delDeviceRes, item, onDestroy])

  if (!item || !curDevice) return <></>

  const { key, icon, name } = curDevice;
  // const { deviceConfig } = data;

  return (
    <div className={styles.container}>
      <div key={key} className={styles.left}>
        <div className={styles.left_icon}>
          <PreloadImage src={icon} alt="icon" />
        </div>
        <span className={styles.left_name}>{name}</span>
        {/* <div className={styles.left_traffic}>
          <span><PreloadImage src={rise} alt="icon" />{`${deviceConfig.rise}KB/s`}</span>
          <span><PreloadImage src={down} alt="icon" />{`${deviceConfig.down}KB/s`}</span>
        </div> */}
      </div>
      <Divider type="vertical" />
      <div className={styles.right}>
        <List dataSource={dataSource} />
        <Divider />
        <span className={styles.right_spanText}>视图</span>
        <List dataSource={deviceDataSource} />
        <Divider />
        <List dataSource={recordSource} />
        <Divider />
        <div className={styles.right_btns}>
          <Button type="text" className={styles.right_btn} onClick={onClear}>清空存档</Button>
          <Button type="text" className={styles.right_btn} onClick={onDelete}>删除设备</Button>
        </div>
      </div>
    </div>
  )
}

export default IPCDesktopDeviceDetail;