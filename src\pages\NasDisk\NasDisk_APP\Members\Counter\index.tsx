import React, { useState, useEffect } from 'react';
import { Toast } from "@/components/Toast/manager";
import styles from './index.module.scss';

interface CounterProps {
  /** 是否显示收银台 */
  isVisible: boolean;
  /** 关闭收银台的回调函数 */
  onClose: () => void;
  /** 支付成功的回调函数 */
  onPaymentSuccess?: () => void;
  /** 支付失败的回调函数 */
  onPaymentError?: (error: string) => void;
  /** 支付取消的回调函数 */
  onPaymentCancel?: () => void;
  /** NAS ID，默认为 '55' */
  nasId?: string;
  /** Access Token，如果不传则使用默认值 */
  accessToken?: string;
  /** 是否显示头部，默认为 true */
  showHeader?: boolean;
  /** 自定义标题 */
  title?: string;
}

const Counter: React.FC<CounterProps> = ({
  isVisible,
  onClose,
  onPaymentSuccess,
  onPaymentError,
  onPaymentCancel,
  nasId = '55',
  accessToken = '123.3ed0f7c25b337f645dd9132285261503.YHgVn2hKTkyxp1tWyiHGJwW0Ylye23bjxonXA6D.XeM75Q',
  showHeader = true,
  title = '开通百度网盘NAS会员'
}) => {
  const [cashierUrl, setCashierUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 初始化收银台URL
  useEffect(() => {
    if (isVisible) {
      const paymentUrl = `https://pan.baidu.com/ent/nas/wap/cashier?nas_id=${nasId}&access_token=${encodeURIComponent(accessToken)}`;
      setCashierUrl(paymentUrl);
      setIsLoading(true);
      
      Toast.show('正在加载支付页面...', { 
        duration: 2000, 
        position: 'top' 
      });
    } else {
      setCashierUrl('');
      setIsLoading(false);
    }
  }, [isVisible, nasId, accessToken]);

  // 监听支付页面的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 检查消息来源是否为百度网盘域名
      if (event.origin !== 'https://pan.baidu.com') {
        return;
      }

      // 处理支付相关消息
      if (event.data && typeof event.data === 'object') {
        const { type, status } = event.data;
        
        if (type === 'payment') {
          if (status === 'success') {
            // 支付成功
            Toast.show('支付成功！会员已开通', { 
              duration: 3000, 
              position: 'top' 
            });
            onPaymentSuccess?.();
            onClose();
          } else if (status === 'cancel') {
            // 用户取消支付
            Toast.show('支付已取消', { 
              duration: 2000, 
              position: 'top' 
            });
            onPaymentCancel?.();
            onClose();
          } else if (status === 'error') {
            // 支付失败
            const errorMsg = '支付失败，请重试';
            Toast.show(errorMsg, { 
              duration: 3000, 
              position: 'top' 
            });
            onPaymentError?.(errorMsg);
          }
        }
      }
    };

    if (isVisible) {
      // 添加消息监听器
      window.addEventListener('message', handleMessage);
    }

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [isVisible, onClose, onPaymentSuccess, onPaymentError, onPaymentCancel]);

  // 处理支付页面加载完成
  const handlePaymentLoad = () => {
    setIsLoading(false);
    Toast.show('支付页面加载完成', { 
      duration: 1500, 
      position: 'top' 
    });
  };

  // 处理关闭
  const handleClose = () => {
    onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.counterOverlay}>
      {showHeader && (
        <div className={styles.counterHeader}>
          <div className={styles.counterTitle}>{title}</div>
          <div className={styles.counterClose} onClick={handleClose}>
            ✕
          </div>
        </div>
      )}
      
      <div className={styles.counterContainer}>
        {isLoading && (
          <div className={styles.loadingOverlay}>
            <div className={styles.loadingSpinner}></div>
            <div className={styles.loadingText}>正在加载支付页面...</div>
          </div>
        )}
        
        {cashierUrl && (
          <iframe
          title='百度支付收银台'
            src={cashierUrl}
            className={styles.counterIframe}
            frameBorder="0"
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              display: isLoading ? 'none' : 'block'
            }}
            allow="payment"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
            onLoad={handlePaymentLoad}
          />
        )}
      </div>
    </div>
  );
};

export default Counter;
