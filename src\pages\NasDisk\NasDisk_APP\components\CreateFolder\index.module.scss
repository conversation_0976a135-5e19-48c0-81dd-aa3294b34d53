// 弹窗样式
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background-color: white;
  border-radius: 16px;
  width: 100%;
  max-width: 320px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modalHeader {
  position: relative;
  padding: 24px 20px 16px;
  text-align: center;
  
  .modalTitle {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
  }
  
  .modalClose {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #999999;
    cursor: pointer;
    border-radius: 50%;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:hover {
        background-color: transparent;
      }
    }
  }
}

.modalBody {
  padding: 0 20px 24px;
  
  .inputContainer {
    position: relative;
    
    .folderNameInput {
      width: 100%;
      height: 48px;
      border: 2px solid #32BAC0;
      border-radius: 8px;
      padding: 0 40px 0 16px;
      font-size: 16px;
      outline: none;
      color: #333333;
      box-sizing: border-box;
      
      &::placeholder {
        color: #999999;
      }
      
      &:focus {
        border-color: #32BAC0;
      }
    }
    
    .clearButton {
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #cccccc;
      border-radius: 50%;
      font-size: 14px;
      color: white;
      cursor: pointer;
      
      &:hover {
        background-color: #999999;
      }
    }
  }
}

.modalFooter {
  display: flex;
  gap: 12px;
  padding: 0 20px 24px;
  
  .cancelButton {
    flex: 1;
    height: 48px;
    border: 1px solid #cccccc;
    border-radius: 24px;
    background-color: white;
    color: #666666;
    font-size: 16px;
    font-weight: 500;
    
    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:active {
        background-color: white;
      }
    }
  }
  
  .confirmButton {
    flex: 1;
    height: 48px;
    border: none;
    border-radius: 24px;
    background-color: #32BAC0;
    color: white;
    font-size: 16px;
    font-weight: 500;
    
    &:active {
      background-color: #2A9EA3;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: #32BAC0;
      
      &:active {
        background-color: #32BAC0;
      }
    }
  }
}
