// ToastItem.tsx
import React, { useCallback, useEffect, useState } from 'react';
import './index.css';
interface Props {
  content: React.ReactNode;
  duration?: number;
  position?: 'top' | 'bottom';
  className?: string;
  closable?: boolean;
  onRemove: () => void;
}

const ToastItem: React.FC<Props> = ({
  content,
  duration = 0,
  position = 'top',
  className = '',
  closable = false,
  onRemove
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const handleClose = useCallback(() => {
    setIsVisible(false);
    setTimeout(onRemove, 300); // 等待动画结束
  }, [onRemove]);

  useEffect(() => {
    setIsVisible(true);
    if (duration === 0) return;
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, handleClose]);

  return (
    <div
      className={`toast-item ${position} ${className} ${isVisible ? 'enter' : 'exit'
        }`}
    >
      <div className="toast-content">{content}</div>
      {closable && (
        <button className="toast-close" onClick={handleClose}>
          ×
        </button>
      )}
    </div>
  );
};

export default ToastItem;