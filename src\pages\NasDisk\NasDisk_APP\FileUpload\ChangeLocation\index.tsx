import React, { useState, useEffect, useCallback } from 'react';
import { Button, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { useHistory, useLocation } from 'react-router-dom';
import CreateFolder from '@/pages/NasDisk/NasDisk_APP/components/CreateFolder';
import { useRequest } from 'ahooks';
import { getBaiduNetdiskFileList, BaiduFileItem, createBaiduNetdiskFolder } from '@/api/nasDisk';
import { PreloadImage } from '@/components/Image';
import folderIcon from '@/Resources/nasDiskImg/file-icon.png';

// 内部使用的文件项接口
interface FileItem {
  fs_id: number;
  name: string;
  path: string;
  type: 'folder' | 'file';
  time: string;
  size: number;
}

interface LocationState {
  from?: string;
  isVip?: boolean;
  selectedFolders?: string[];
  sourceType?: 'fileUpload';
}

const ChangeLocation: React.FC = () => {
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { from, isVip, selectedFolders, sourceType } = location.state || {};
  
  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);
  
  // 选中的文件夹路径
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  
  // 当前路径面包屑
  const [breadcrumbPath, setBreadcrumbPath] = useState<{label: string, path: string}[]>([
    { label: "百度网盘", path: "/" }
  ]);
  
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);
  
  // 新建文件夹弹窗状态
  const [showCreateFolderModal, setShowCreateFolderModal] = useState<boolean>(false);
  
  // 当前目录路径（用于创建文件夹）
  const [currentDirectoryPath, setCurrentDirectoryPath] = useState<string>('/');

  // 获取文件列表
  const { run: fetchFileList } = useRequest(
    (params: { path: string }) => {
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: params.path,
        order: "name",
        desc: 0,
        web: 1,
        folder: 1 // 只返回文件夹
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const folders: FileItem[] = response.list
            .filter((item: BaiduFileItem) => item.isdir === 1) // 只保留文件夹
            .map((item: BaiduFileItem) => ({
              fs_id: item.fs_id,
              name: item.server_filename,
              type: "folder",
              time: new Date(item.server_mtime * 1000).toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              }),
              path: item.path,
              size: item.size
            }));

          setFileList(folders);
          setLoading(false);
        } else {
          console.error("获取文件列表失败:", response);
          Toast.show({
            content: "获取文件列表失败，请重试",
            position: "bottom",
            duration: 2000,
          });
          setFileList([]);
          setLoading(false);
        }
      },
      onError: (error) => {
        console.error("获取文件列表失败：", error);
        Toast.show({
          content: "获取文件列表失败，请重试",
          position: "bottom",
          duration: 2000,
        });
        setFileList([]);
        setLoading(false);
      },
    }
  );

  // 创建文件夹请求
  const { run: runCreateFolder } = useRequest(
    (params: { path: string }) => {
      return createBaiduNetdiskFolder({
        action: "createdir",
        path: params.path
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response.errno === 0 || response.code === 0) {
          Toast.show({
            content: "创建文件夹成功",
            position: "bottom",
            duration: 2000,
          });
          handleCreateFolderSuccess();
        } else {
          Toast.show({
            content: response.errmsg || "创建文件夹失败，请重试",
            position: "bottom",
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("创建文件夹失败：", error);
        Toast.show({
          content: "创建文件夹失败，请重试",
          position: "bottom",
          duration: 2000,
        });
      }
    }
  );

  // 初始化时获取根目录文件列表
  useEffect(() => {
    setLoading(true);
    fetchFileList({ path: '/' });
  }, [fetchFileList]);

  // 处理文件夹点击 - 导航到子文件夹
  const handleFolderClick = useCallback((folder: FileItem) => {
    // 设置选中的文件夹
    setSelectedFolder(folder.path);
    
    // 导航到子文件夹
    setLoading(true);
    
    // 构建新路径
    const newPath = folder.path;
    
    // 更新面包屑
    setBreadcrumbPath(prev => [...prev, { label: folder.name, path: newPath }]);
    
    // 更新当前路径
    setCurrentDirectoryPath(newPath);
    
    // 获取子文件夹内容
    fetchFileList({ path: newPath });
  }, [fetchFileList]);

  // 处理面包屑点击 - 导航到指定目录
  const handleBreadcrumbClick = useCallback((item: {label: string, path: string}, index: number) => {
    // 如果点击的是当前位置，不做任何操作
    if (index === breadcrumbPath.length - 1) {
      return;
    }
    
    setLoading(true);
    
    // 截取到点击的位置
    const newPath = breadcrumbPath.slice(0, index + 1);
    setBreadcrumbPath(newPath);
    
    // 获取指定路径的目录列表
    const targetPath = item.path;
    
    // 更新当前路径
    setCurrentDirectoryPath(targetPath);
    
    // 获取文件列表
    fetchFileList({ path: targetPath });
    
    // 清除选中状态
    setSelectedFolder('');
  }, [breadcrumbPath, fetchFileList]);

  // 处理返回
  const handleBack = () => {
    // 如果有来源页面，则返回到该页面
    if (from) {
      history.push({
        pathname: from,
        state: {
          isVip: isVip,
          selectedFolders: selectedFolders,
          sourceType: sourceType
        }
      });
    } else {
      history.goBack();
    }
  };

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setShowCreateFolderModal(true);
  };

  // 处理创建文件夹成功
  const handleCreateFolderSuccess = () => {
    setShowCreateFolderModal(false);
    
    // 刷新当前目录
    setLoading(true);
    fetchFileList({ path: currentDirectoryPath });
  };

  // 处理创建文件夹取消
  const handleCreateFolderCancel = () => {
    setShowCreateFolderModal(false);
  };

  // 创建新文件夹
  const createFolder = (folderName?: string) => {
    if (!folderName) {
      Toast.show({
        content: "请输入文件夹名称",
        position: "bottom",
        duration: 2000,
      });
      return;
    }
    
    // 构建完整路径
    const newFolderPath = currentDirectoryPath === '/' 
      ? `/${folderName}` 
      : `${currentDirectoryPath}/${folderName}`;
    
    // 调用创建文件夹接口
    runCreateFolder({ path: newFolderPath });
  };

  // 处理确定按钮
  const handleConfirm = () => {
    if (!selectedFolder && breadcrumbPath.length <= 1) {
      Toast.show({
        content: '请选择一个文件夹',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 使用当前路径或选中的文件夹路径
    const uploadPath = selectedFolder || currentDirectoryPath;
    
    // 获取完整显示路径
    const fullDisplayPath = breadcrumbPath.map(item => item.label).join('/');
    
    // 返回上一页，并传递选择的路径信息
    if (from) {
      history.push({
        pathname: from,
        state: { 
          uploadPath: uploadPath,
          uploadDisplayPath: fullDisplayPath,
          isVip: isVip,
          selectedFolders: selectedFolders,
          sourceType: sourceType
        }
      });
    } else {
      history.goBack();
    }
  };

  return (
    <div className={styles.uploadBDContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar onBack={handleBack} />
        <div className={styles.title}>更改上传位置</div>
        {/* 面包屑导航 */}
        <div className={styles.breadcrumb}>
          {breadcrumbPath.map((item, index) => (
            <React.Fragment key={index}>
              <span 
                className={`${styles.breadcrumbItem} ${index === breadcrumbPath.length - 1 ? styles.active : ''}`}
                onClick={() => handleBreadcrumbClick(item, index)}
              >
                {item.label}
              </span>
              {index < breadcrumbPath.length - 1 && (
                <span className={styles.breadcrumbSeparator}>&gt;</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {!loading && fileList.map(folder => (
            <div 
              key={folder.fs_id}
              className={`${styles.fileItem} ${selectedFolder === folder.path ? styles.selected : ''}`}
              onClick={() => handleFolderClick(folder)}
            >
              <div className={styles.fileIcon}>
                <PreloadImage
                  src={folderIcon}
                  alt=""
                  style={{ width: 40, height: 40 }}
                />
              </div>
              <div className={styles.fileInfo}>
                <div className={styles.fileName}>
                  {folder.name}
                </div>
                <div className={styles.fileDetails}>
                  {folder.time}
                </div>
              </div>
            </div>
          ))}
          
          {!loading && fileList.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className={styles.footer}>
        <Button 
          className={styles.leftBut}
          onClick={handleCreateFolder}
        >
          + 新建文件夹
        </Button>
        <Button 
          className={styles.rightBut}
          onClick={handleConfirm}
        >
          确定
        </Button>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateFolder
        visible={showCreateFolderModal}
        onCancel={handleCreateFolderCancel}
        onSuccess={createFolder}
        currentPath={currentDirectoryPath}
      />
    </div>
  );
};

export default ChangeLocation;
