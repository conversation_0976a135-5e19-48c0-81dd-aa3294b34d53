import { PreloadImage } from "@/components/Image";
import styles from './index.module.scss';
import TVFocusable, { FocusableElement } from "@/pages/FATWall/FATWall_TV/TVFocus";
import like_icon from "@/Resources/filmWall/like.png";

export interface IFilmCard {
  url: string;
  score: string;
  name: string;
  title: string;
  isDrama?: any;
  favourite: boolean
}

const FilmCard = (props: IFilmCard & {
  row: number;
  col: number;
  id: string;
} & { currentItemCallback?: (item: FocusableElement, e: KeyboardEvent) => void, onClick?: () => void }) => {
  const { url, score, name, title, row, col, id, currentItemCallback, onClick, favourite } = props;
  return (
    <div className={styles.container} key={name}>
      <TVFocusable id={id} row={row} col={col} className={styles.img_container} currentItem={currentItemCallback} onClick={onClick}>
        <div className={styles.img_content}>
          <PreloadImage src={url} alt="img" />
          <div className={styles.score}>{score}</div>
          {
            favourite && (
              <div className={styles.favourite}>
                <PreloadImage src={like_icon} alt="like_img" />
              </div>
            )
          }
        </div>
      </TVFocusable>
      <div className={styles.title} title={title}>{title}</div>
    </div>
  )
}

export default FilmCard;