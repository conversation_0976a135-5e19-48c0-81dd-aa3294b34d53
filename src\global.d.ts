// 扩展 Window 接口，添加全局变量声明
interface Window {
    __MICRO_APP_ENVIRONMENT__?: boolean; // 判断是否在微前端环境
    microApp?: {
      dispatch: (data: any) => void; // 发送数据到主应用
      addDataListener: (callback: (data: any) => void) => void; // 监听主应用发送的数据
      removeDataListener: () => void; // 移除监听
    };
    hs_callHandler?: (
      methodName: string,
      params: any,
      callback: (response: string) => void
    ) => void; // 调用 App 提供的方法
    hs_registerHandler?: (
      methodName: string,
      handler: (...args: any[]) => void
    ) => void; // 注册供 App 调用的方法
  }
declare module '*.scss' {
    const content: { [className: string]: string };
    export default content;
}