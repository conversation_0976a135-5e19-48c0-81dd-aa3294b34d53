.synchronizationContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color, #ffffff);
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color, #ffffff);
}

.title {
  font-size: 24px;
  padding: 0 16px;
  color: var(--text-color, #000000);
  margin-bottom: 12px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color, #ffffff);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  .breadcrumbItem {
    color: rgba(255, 178, 29, 1);
    background-color: #FFF4DD;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    flex-shrink: 0;
    
    &.active {
      background-color: rgba(255, 178, 29, 0.2);
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 12px;
    flex-shrink: 0;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; // 为底部按钮留出空间
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--secondary-text-color, #8C93B0);
  
  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;
  
  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    // border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .folderIcon {
        width: 100%;
        height: 100%;
        background-color: #FFC14F;
        border-radius: 4px;
        mask-size: cover;
        -webkit-mask-size: cover;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
        -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
      }
    }
    
    .fileInfo {
      flex: 1;
      overflow: hidden;
      
      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color, #000000);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        
        .heartIcon {
          margin-left: 8px;
          color: #FF5C5C;
          font-size: 14px;
          flex-shrink: 0;
        }
      }
      
      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .checkboxContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 12px;
    }
  }
  
  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 14px;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: var(--background-color);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  
  .leftBut{
    width: 167px;
    height: 48px;
    border-radius: 30px;
    background-color: #fff;
    // border: none;
    text-align: start;
    font-size: 13px;
    font-weight: 500;
    color:rgba(116, 197, 255, 1);
    
    &:active {
      background-color: #555;
    }
    
    &:disabled {
      background-color: #ccc;
      opacity: 0.6;
    }
  }
  .rightBut {
    width: 167px;
    height: 48px;
    border-radius: 30px;
    background-color: #32BAC0;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    
    &:active {
      background-color: #2A9EA3;
    }
    
    &:disabled {
      background-color: #999999;
      opacity: 1;
      cursor: not-allowed;
    }
  }
}
