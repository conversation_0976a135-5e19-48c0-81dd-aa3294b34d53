.container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 35px);

  .header {
    padding: 0 10px;
    .backIcon {
      width: 40px;
      height: 40px;
    }
  }
  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--text-color);
    padding: 10px 16px;
  }
}

.settingsCard {
  background-color: var(--background-color);
  border-radius: 12px;
  overflow: hidden;

  .cardTitle {
    padding: 16px 16px 10px;
    font-size: 14px;
    color: #8c93b0;
  }
}

.settingsList {
  --border-inner: 0;
  --border-bottom: 0;
  --border-top: 0;
  padding: 0 10px;
  :global {
    .adm-list-item {
      padding-left: 8px;
    }
  }

  .settingItem {
    --padding-left: 16px;
    --padding-right: 16px;
    background-color: var(--background-color);

    .settingContent {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .settingLabel {
        font-size: 16px;
        color: var(--text-color);
      }

      .settingValue {
        font-size: 14px;
        color: var(--list-value-text-color);
        line-height: 24px;
      }
    }
  }
}

.note {
  padding: 12px 20px;
  font-size: 14px;
  color: #8c93b0;
  line-height: 1.5;
  background-color: var(--background-color);
  border-radius: 8px;
  margin-top: 29vh;
}

.footer {
  margin-top: auto;
  padding: 24px;
  background-color: var(--background-color);

  .nextButton {
    --border-radius: 16px;
    height: 48px;
    font-size: 16px;
    background-color: var(--primary-color);
    border: none;
  }
}

.customArrow {
  margin-right: 5px;
}
