import React, { useState } from "react";
import { Modal, Input, Button, message } from "antd";
import { CloseCircleFilled, CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { WebDavInfo } from "@/utils/DeviceType";
interface CreateFolderProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (folderName: string) => void;
  currentPath: string;
  webDavConfig?: WebDavInfo; // Add webDavConfig prop
}

const CreateFolder: React.FC<CreateFolderProps> = ({
  visible,
  onCancel,
  onSuccess,
  currentPath,
  webDavConfig, // Add webDavConfig to props
}) => {
  const [folderName, setFolderName] = useState<string>("");
  const [isCreating, setIsCreating] = useState<boolean>(false);

  // 使用WebDAV MKCOL方法创建文件夹
  const createDirectoryWithWebDAV = async (
    path: string
  ): Promise<{ success: boolean; message: string }> => {
    if (!webDavConfig) {
      return { success: false, message: "WebDAV配置未获取到" };
    }
    
    try {
      // 确保路径格式正确
      let dirPath = path;
      if (!dirPath.startsWith('/')) {
        dirPath = '/' + dirPath;
      }
      
      // 从路径中移除 alias_root 前缀
      if (webDavConfig.alias_root && dirPath.startsWith(webDavConfig.alias_root)) {
        dirPath = dirPath.substring(webDavConfig.alias_root.length);
      }
      
      // 使用encodeURIComponent处理文件夹名称部分
      const pathParts = dirPath.split('/');
      const folderNameEncoded = encodeURIComponent(pathParts[pathParts.length - 1]);
      pathParts[pathParts.length - 1] = folderNameEncoded;
      const encodedPath = pathParts.join('/');
      
      // 获取当前主机，优先使用实际IP地址
      const hostWithoutPort = window.location.hostname || window.location.host.replace(/:\d+$/, "");
      // const hostWithoutPort = '*************';
      
      // 构建WebDAV URL
      const webDAVUrl = `https://${hostWithoutPort}:${webDavConfig.port}${encodedPath}`;
      
      // 使用WebDAV MKCOL方法创建文件夹
      const response = await fetch(webDAVUrl, {
        method: "MKCOL",
        headers: {
          'Depth': '1',
          'Content-Type': 'application/xml',
          'Authorization': 'Basic ' + btoa(`${webDavConfig.username}:${webDavConfig.password}`),
          'Range': 'bytes=0-1',
        },
      });

      if (response.status === 201) {
        // 201 Created - 文件夹创建成功
        return { success: true, message: "文件夹创建成功" };
      } else if (response.status === 405) {
        // 405 Method Not Allowed - 文件夹已存在
        return { success: false, message: "文件夹已存在" };
      } else if (response.status === 409) {
        // 409 Conflict - 父目录不存在
        return { success: false, message: "父目录不存在" };
      } else if (response.status === 403) {
        // 403 Forbidden - 权限不足
        return { success: false, message: "权限不足，无法创建文件夹" };
      } else {
        return {
          success: false,
          message: `创建失败，状态码: ${response.status}`,
        };
      }
    } catch (error) {
      console.error("WebDAV MKCOL 请求失败:", error);
      return { success: false, message: "网络请求失败，请检查连接" };
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | any) => {
    setFolderName(e.target.value);
  };

  // 清除输入
  const handleClearInput = () => {
    setFolderName("");
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && folderName.trim()) {
      handleConfirm();
    }
  };

  // 创建文件夹
  const handleConfirm = async () => {
    if (!folderName.trim()) {
      message.error("请输入文件夹名称");
      return;
    }

    // 检查文件夹名称是否包含特殊字符
    const invalidChars = /[\\/:*?"<>|]/;
    if (invalidChars.test(folderName)) {
      message.error('文件夹名称不能包含以下字符: \\ / : * ? " < > |');
      return;
    }

    // 如果没有 webDavConfig，则直接调用 onSuccess 传递文件夹名称
    if (!webDavConfig) {
      // 直接返回文件夹名称给父组件处理
      const trimmedFolderName = folderName.trim();
      setFolderName("");
      onSuccess(trimmedFolderName);
      return;
    }

    setIsCreating(true);

    try {
      // 构建完整路径
      let folderPath = currentPath;
      if (!folderPath.endsWith('/')) {
        folderPath += '/';
      }
      folderPath += folderName.trim();
      
      // 使用WebDAV创建文件夹
      const result = await createDirectoryWithWebDAV(folderPath);

      if (result.success) {
        message.success("文件夹创建成功");
        const trimmedFolderName = folderName.trim();
        setFolderName("");
        onSuccess(trimmedFolderName);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error("创建文件夹失败:", error);
      message.error("创建文件夹失败");
    } finally {
      setIsCreating(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setFolderName("");
    onCancel();
  };

  return (
    <Modal
      title="新建文件夹"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={400}
      centered
      maskClosable={!isCreating}
      closeIcon={<CloseOutlined style={{ color: "var(--title-color)" }} />}
      className={styles.createFolderModal}
    >
      <div className={styles.inputWrapper}>
        <Input
          value={folderName}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder="请输入文件夹名称"
          autoFocus
          disabled={isCreating}
          maxLength={255}
          suffix={
            folderName ? (
              <CloseCircleFilled
                className={styles.clearIcon}
                onClick={handleClearInput}
              />
            ) : null
          }
        />
      </div>
      <div className={styles.buttonGroup}>
        <Button
          className={styles.cancelButton}
          onClick={handleCancel}
          disabled={isCreating}
        >
          取消
        </Button>
        <Button
          type="primary"
          className={styles.confirmButton}
          onClick={handleConfirm}
          loading={isCreating}
          disabled={!folderName.trim() || isCreating}
        >
          确定
        </Button>
      </div>
    </Modal>
  );
};

export default CreateFolder;
