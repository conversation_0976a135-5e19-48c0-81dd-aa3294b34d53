import React, { useState, useEffect } from "react";
import { List, Switch, Button, Toast, Popup, Input } from "antd-mobile";
import { useHistory, useLocation } from "react-router-dom";
// import { useStorage } from "../Context/storageContext";
import styles from "./index.module.scss";
import UnitSelector from "@/components/UnitSelector";
import PopoverSelector from "@/components/PopoverSelector";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import filter from "@/Resources/camMgmtImg/filter.png";
import enterRight from "@/Resources/camMgmtImg/enter-right.png";
import polygon from "@/Resources/camMgmtImg/polygon.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import filterDark from "@/Resources/camMgmtImg/filter-dark.png";
import enterRightDark from "@/Resources/camMgmtImg/enter-right-dark.png";
import polygonDark from "@/Resources/camMgmtImg/polygon-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { getRecordConfig, setupRecordCamera, CameraInfo } from "@/api/ipc";
import { useRequest } from "ahooks";

interface LocationState {
  camera?: CameraInfo;
}

// 默认配置
const defaultConfig = {
  storagePath: "路径1/路径2",
  dynamicBackup: true,
  duration: "无限制",
  storageCapacity: "50",
  storageUnit: "GB",
  handleStrategy: "滚动覆盖",
  warningThreshold: "90%",
  recordPlan: "全时段",
  recordMode: "连续录制",
  detectSource: "智能存储",
  detectEvent: "5项",
  preRecordDuration: "5秒",
  postRecordDuration: "5秒",
  timeServer: "摄像机",
};

// 将MB转换为适当的单位和值
const formatStorageSize = (sizeMB: number) => {
  if (isNaN(sizeMB) || sizeMB <= 0) {
    return { value: "50", unit: "GB" };
  }

  if (sizeMB >= 1024 * 1024) {
    // 转换为TB
    return {
      value: (sizeMB / (1024 * 1024)).toFixed(0),
      unit: "TB"
    };
  } else if (sizeMB >= 1024) {
    // 转换为GB
    return {
      value: (sizeMB / 1024).toFixed(0),
      unit: "GB"
    };
  } else {
    // 保持MB
    return {
      value: sizeMB.toFixed(0),
      unit: "MB"
    };
  }
};

// 将单位和值转换为MB
const convertToMB = (value: string, unit: string): number => {
  const numValue = parseInt(value);
  if (isNaN(numValue)) return 0;

  switch (unit) {
    case "TB":
      return numValue * 1024 * 1024;
    case "GB":
      return numValue * 1024;
    case "MB":
    default:
      return numValue;
  }
};

const RecordingConfig = () => {
  const history = useHistory();
  // const { setThreshold } = useStorage();
  const { isDarkMode } = useTheme();
  const location = useLocation<LocationState>();

  // 获取传递的摄像机数据
  const [camera, setCamera] = useState<CameraInfo | null>(null);
  const [, setConfigLoaded] = useState(false);

  // 配置项状态管理
  const [config, setConfig] = useState(defaultConfig);

  // 获取摄像机ID
  useEffect(() => {
    if (location.state?.camera) {
      setCamera(location.state.camera);
      sessionStorage.setItem('record_config_camera', JSON.stringify(location.state.camera));
    } else {
      const cache = sessionStorage.getItem('record_config_camera');
      if (cache) {
        try {
          setCamera(JSON.parse(cache));
        } catch {}
      }
    }
  }, [location.state]);

  // 获取录制配置
  const { run: fetchConfig } = useRequest(getRecordConfig, {
    manual: true,
    onSuccess: (result) => {
      console.log('获取录制配置成功:', result);
      const recordConfig = result.data;

      // 处理存储空间大小和单位
      const storageSizeFormatted = formatStorageSize(recordConfig.space_limit || 0);

      // 将API返回的数据映射到UI配置
      setConfig({
        storagePath: recordConfig.path || defaultConfig.storagePath,
        dynamicBackup: recordConfig.backup || defaultConfig.dynamicBackup,
        duration: recordConfig.retention_period ? `${recordConfig.retention_period}天` : defaultConfig.duration,
        storageCapacity: storageSizeFormatted.value,
        storageUnit: storageSizeFormatted.unit,
        handleStrategy: recordConfig.space_limit_policy === "replace" ? "滚动覆盖" : "停止录制",
        warningThreshold: "90%",
        recordPlan: recordConfig.record_schedule?.type === "all" ? "全时段" : "自定义",
        recordMode: recordConfig.record_mode === "event" ? "事件触发" : "连续录制",
        detectSource: recordConfig.event_source === "nas" ? "智能存储" : "摄像机",
        detectEvent: recordConfig.event_trigger ? `${recordConfig.event_trigger.filter(e => e.enabled).length}项` : defaultConfig.detectEvent,
        preRecordDuration: recordConfig.record_bf_event ? `${recordConfig.record_bf_event}秒` : defaultConfig.preRecordDuration,
        postRecordDuration: recordConfig.record_af_event ? `${recordConfig.record_af_event}秒` : defaultConfig.postRecordDuration,
        timeServer: recordConfig.ntp_source === "nas" ? "智能存储" : "摄像机",
      });

      // 设置初始容量输入值
      setCapacityInput(storageSizeFormatted.value);
      setConfigLoaded(true);
    },
    onError: (error) => {
      console.error('获取录制配置失败:', error);
      setConfigLoaded(true);
    }
  });

  // 当摄像机ID可用时获取配置
  useEffect(() => {
    if (camera?.did) {
      fetchConfig({ camera: camera.did });
    }
  }, [camera, fetchConfig]);

  const { run: saveConfig } = useRequest(setupRecordCamera, {
    manual: true,
    onSuccess: (result) => {
      console.log('保存配置成功:', result);
      Toast.show({
        content: '设置成功',
        position: 'bottom',
      });
    },
    onError: (error) => {
      console.error('保存配置失败:', error);
      Toast.show({
        content: '设置失败，请重试',
        position: 'bottom',
      });
    }
  });

  // Popover可见性状态
  const [durationPopoverVisible, setDurationPopoverVisible] = useState(false);
  const [strategyPopoverVisible, setStrategyPopoverVisible] = useState(false);
  const [modePopoverVisible, setModePopoverVisible] = useState(false);
  // const [sourcePopoverVisible, setSourcePopoverVisible] = useState(false);
  // const [timeServerVisible, setTimeServerVisible] = useState(false);
  const [, setThresholdPopupVisible] = useState(false);
  const [capacityPopupVisible, setCapacityPopupVisible] = useState(false);
  const [unitSelectorVisible, setUnitSelectorVisible] = useState(false);

  // 输入相关状态
  const [, setThresholdInput] = useState("90");
  const [capacityInput, setCapacityInput] = useState("50");

  const CustomArrow = () => (
    <img
      alt=""
      src={isDarkMode ? filterDark : filter}
      className={styles.customArrow}
    />
  );

  // 显示配置结果Toast
  const showConfigToast = (success: boolean) => {
    Toast.show({
      content: success ? "设置成功" : "设置失败，请重试",
      position: "bottom",
      duration: 1000,
    });
  };

  // 处理容量预警阀值确认
  // const handleThresholdConfirm = () => {
  //   const value = parseInt(thresholdInput);
  //   if (isNaN(value) || value < 1 || value > 100) {
  //     Toast.show({ content: "请输入1-100之间的数字", position: "center" });
  //     return;
  //   }
  //   setThreshold(value);
  //   setConfig({ ...config, warningThreshold: `${value}%` });
  //   setThresholdPopupVisible(false);
  //   showConfigToast(true);
  // };

  // const handleThresholdInput = (value: string) => {
  //   const numValue = value.replace(/[^0-9]/g, "");
  //   setThresholdInput(numValue);
  // };

  // 处理存档容量确认
  const handleCapacityConfirm = () => {
    const value = parseInt(capacityInput);
    if (isNaN(value) || value <= 0) {
      Toast.show({
        content: "请输入有效的数字",
        position: "center",
      });
      return;
    }

    setConfig({
      ...config,
      storageCapacity: capacityInput,
      storageUnit: config.storageUnit,
    });
    setCapacityPopupVisible(false);
    showConfigToast(true);

    // 更新录制配置 - 转换为MB
    if (camera) {
      const sizeMB = convertToMB(capacityInput, config.storageUnit);
      saveConfig({
        camera: [camera.did],
        config: {
          space_limit: sizeMB,
        }
      });
    }
  };

  const handleCapacityInput = (value: string) => {
    const numValue = value.replace(/[^0-9]/g, "");
    setCapacityInput(numValue);
  };

  const handleUnitChange = (unit: string) => {
    setConfig({ ...config, storageUnit: unit });
  };

  // 通用选项处理函数
  const handleOptionChange = (key: string, value: string) => {
    const configNames: Record<string, string> = {
      duration: "录像保存时长",
      handleStrategy: "容量不足处理策略",
      warningThreshold: "容量预警阀值",
      recordMode: "录制模式",
      detectSource: "检测来源",
      recordPlan: "录制计划",
      detectEvent: "检测事件",
      timeServer: "网络时间服务器",
    };

    setConfig((prev) => ({ ...prev, [key]: value }));

    if (camera) {
      let apiConfig: any = {};

      // 根据UI选项映射到API参数
      if (key === 'duration') {
        const days = parseInt(value.replace('天', ''));
        if (!isNaN(days)) {
          apiConfig.retention_period = days;
        }
      } else if (key === 'handleStrategy') {
        apiConfig.space_limit_policy = value === '滚动覆盖' ? 'replace' : 'stop';
      } else if (key === 'recordMode') {
        apiConfig.record_mode = value === '事件触发' ? 'event' : 'continuous';
      } else if (key === 'detectSource') {
        apiConfig.event_source = value === '智能存储' ? 'nas' : 'camera';
      } else if (key === 'timeServer') {
        apiConfig.ntp_source = value === '智能存储' ? 'nas' : 'camera';
      }

      if (Object.keys(apiConfig).length > 0) {
        saveConfig({
          camera: [camera.did],
          config: apiConfig
        });
      }
    }

    if (configNames[key]) {
      showConfigToast(true);
    }
  };

  // 存档部分选项
  const durationOptions = [
    { label: "7天", value: "7天" },
    { label: "14天", value: "14天" },
    { label: "30天", value: "30天" },
    { label: "90天", value: "90天" },
    { label: "365天", value: "365天" },
    { label: "无限制", value: "无限制" },
  ];

  const strategyOptions = [
    { label: "滚动覆盖", value: "滚动覆盖" },
    { label: "停止录制", value: "停止录制" },
  ];

  // 录制部分选项
  const modeOptions = [
    { label: "事件触发", value: "事件触发" },
    { label: "连续录制", value: "连续录制" },
  ];

  // const sourceOptions = [
  //   { label: "摄像机", value: "摄像机" },
  //   { label: "智能存储", value: "智能存储" },
  // ];

  // const timeServerOption = [
  //   { label: "摄像机", value: "摄像机" },
  //   { label: "智能存储", value: "智能存储" },
  // ];

  const unitOptions = [
    { label: "MB", value: "MB" },
    { label: "GB", value: "GB" },
    { label: "TB", value: "TB" },
  ];

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>录制配置</div>

      <List className={styles.mainList}>
        {/* 存档部分 */}
        <div className={styles.sectionTitle}>存档</div>

        <List.Item
          className={styles.configItem}
          arrow=""
          onClick={() => console.log("修改存放位置")}
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>存放位置</span>
            <span className={styles.itemValue}>{config.storagePath}</span>
          </div>
        </List.Item>

        <List.Item className={styles.configItem} arrow="">
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>动态备份</span>
            <Switch
              checked={config.dynamicBackup}
              onChange={(value) => {
                setConfig({ ...config, dynamicBackup: value });
                if (camera) {
                  saveConfig({
                    camera: [camera.did],
                    config: {
                      backup: value
                    }
                  });
                }
              }}
            />
          </div>
        </List.Item>

        <PopoverSelector
          visible={durationPopoverVisible}
          onVisibleChange={setDurationPopoverVisible}
          value={config.duration}
          options={durationOptions}
          onChange={(value) => handleOptionChange("duration", value)}
          onClose={() => setDurationPopoverVisible(false)}
        >
          <List.Item className={styles.configItem} arrow={<CustomArrow />}>
            <div className={styles.itemContent}>
              <span className={styles.itemLabel}>录像保存时长</span>
              <span className={styles.itemValue}>{config.duration}</span>
            </div>
          </List.Item>
        </PopoverSelector>

        <List.Item
          className={styles.configItem}
          onClick={() => {
            setCapacityInput(config.storageCapacity);
            setCapacityPopupVisible(true);
          }}
          arrow={
            <img
              alt=""
              src={isDarkMode ? enterRightDark : enterRight}
              className={styles.customArrow}
            />
          }
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>录像存档容量</span>
            <span className={styles.itemValue}>
              {config.storageCapacity} {config.storageUnit}
            </span>
          </div>
        </List.Item>

        <PopoverSelector
          visible={strategyPopoverVisible}
          onVisibleChange={setStrategyPopoverVisible}
          value={config.handleStrategy}
          options={strategyOptions}
          onChange={(value) => handleOptionChange("handleStrategy", value)}
          onClose={() => setStrategyPopoverVisible(false)}
        >
          <List.Item className={styles.configItem} arrow={<CustomArrow />}>
            <div className={styles.itemContent}>
              <span className={styles.itemLabel}>容量不足处理策略</span>
              <span className={styles.itemValue}>{config.handleStrategy}</span>
            </div>
          </List.Item>
        </PopoverSelector>

        <List.Item
          className={styles.configItem}
          style={{ paddingBottom: 24 }}
          onClick={() => {
            setThresholdInput(config.warningThreshold.replace("%", ""));
            setThresholdPopupVisible(true);
          }}
          // arrow={
          //   <img
          //     alt=""
          //     src={isDarkMode ? enterRightDark : enterRight}
          //     className={styles.customArrow}
          //   />
          // }
          arrow={false}
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>容量预警阀值</span>
            <span className={styles.itemValue}>{config.warningThreshold}</span>
          </div>
        </List.Item>

        <div className={styles.thinLine} />

        {/* 录制部分 */}
        <div className={styles.sectionTitle} style={{ paddingTop: 24 }}>
          录制
        </div>

        <List.Item
          className={styles.configItem}
          arrow={
            <img
              alt=""
              src={isDarkMode ? enterRightDark : enterRight}
              className={styles.customArrow}
            />
          }
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>录制计划</span>
            <span
              className={styles.itemValue}
              onClick={() =>
                history.push(
                  {
                    pathname: "/cameraManagement_app/storageManagement/recordPlan",
                    state: { camera }
                  }
                )
              }
            >
              {config.recordPlan}
            </span>
          </div>
        </List.Item>

        <PopoverSelector
          visible={modePopoverVisible}
          onVisibleChange={setModePopoverVisible}
          value={config.recordMode}
          options={modeOptions}
          onChange={(value) => handleOptionChange("recordMode", value)}
          onClose={() => setModePopoverVisible(false)}
        >
          <List.Item className={styles.configItem} arrow={<CustomArrow />}>
            <div className={styles.itemContent}>
              <span className={styles.itemLabel}>录制模式</span>
              <span className={styles.itemValue}>{config.recordMode}</span>
            </div>
          </List.Item>
        </PopoverSelector>

        {/* <PopoverSelector
          visible={sourcePopoverVisible}
          onVisibleChange={setSourcePopoverVisible}
          value={config.detectSource}
          options={sourceOptions}
          onChange={(value) => handleOptionChange("detectSource", value)}
          onClose={() => setSourcePopoverVisible(false)}
        >
          <List.Item className={styles.configItem} arrow={<CustomArrow />}>
            <div className={styles.itemContent}>
              <span className={styles.itemLabel}>检测来源</span>
              <span className={styles.itemValue}>{config.detectSource}</span>
            </div>
          </List.Item>
        </PopoverSelector> */}

        <List.Item
          className={styles.configItem}
          arrow={
            <img
              alt=""
              src={isDarkMode ? enterRightDark : enterRight}
              className={styles.customArrow}
            />
          }
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>检测事件</span>
            <span
              className={styles.itemValue}
              onClick={() =>
                history.push(
                  {
                    pathname: "/cameraManagement_app/storageManagement/detectEvent",
                    state: { camera }
                  }
                )
              }
            >
              {config.detectEvent}
            </span>
          </div>
        </List.Item>

        {/* <List.Item
          className={styles.configItem}
          arrow=""
          onClick={() => {
            if (camera) {
              const seconds = parseInt(config.preRecordDuration.replace('秒', ''));
              const newValue = seconds === 5 ? 10 : 5;
              setConfig({ ...config, preRecordDuration: `${newValue}秒` });

              saveConfig({
                camera: [camera.did],
                config: {
                  record_bf_event: newValue
                }
              });
            }
          }}
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>事件前录制时长</span>
            <span className={styles.itemValue}>{config.preRecordDuration}</span>
          </div>
        </List.Item>

        <List.Item
          className={styles.configItem}
          // style={{ paddingBottom: 24 }}
          arrow=""
          onClick={() => {
            if (camera) {
              const seconds = parseInt(config.postRecordDuration.replace('秒', ''));
              const newValue = seconds === 5 ? 10 : 5;
              setConfig({ ...config, postRecordDuration: `${newValue}秒` });

              saveConfig({
                camera: [camera.did],
                config: {
                  record_af_event: newValue
                }
              });
            }
          }}
        >
          <div className={styles.itemContent}>
            <span className={styles.itemLabel}>事件后录制时长</span>
            <span className={styles.itemValue}>
              {config.postRecordDuration}
            </span>
          </div>
        </List.Item> */}

        {/* <div className={styles.thinLine} /> */}

        {/* 时间同步部分 */}
        {/* <div className={styles.sectionTitle} style={{ paddingTop: 24 }}>
          时间同步
        </div> */}

        {/* <PopoverSelector
          visible={timeServerVisible}
          onVisibleChange={setTimeServerVisible}
          value={config.timeServer}
          options={timeServerOption}
          onChange={(value) => handleOptionChange("timeServer", value)}
          onClose={() => setTimeServerVisible(false)}
        >
          <List.Item className={styles.configItem} arrow={<CustomArrow />}>
            <div className={styles.itemContent}>
              <span className={styles.itemLabel}>网络时间服务器</span>
              <span className={styles.itemValue}>{config.timeServer}</span>
            </div>
          </List.Item>
        </PopoverSelector> */}
      </List>

      {/* 弹出层保持不变 */}
      {/* <Popup
        visible={thresholdPopupVisible}
        onMaskClick={() => setThresholdPopupVisible(false)}
        position="bottom"
        bodyStyle={{
          borderRadius: "16px",
          margin: "0 12px 12px 12px",
          width: "calc(100% - 24px)",
          maxWidth: "calc(100% - 24px)",
        }}
      >
        <div className={styles.thresholdPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>容量预警阀值(%)</div>
          </div>

          <div className={styles.popupBody}>
            <Input
              type="number"
              className={styles.thresholdInput}
              value={thresholdInput}
              onChange={handleThresholdInput}
              onFocus={(e) => e.target.select()}
              placeholder="输入范围：1-100"
              autoFocus
              clearable
            />

            <div className={styles.popupButtons}>
              <Button
                className={styles.cancelButton}
                onClick={() => setThresholdPopupVisible(false)}
              >
                取消
              </Button>
              <Button
                className={styles.confirmButton}
                color="primary"
                onClick={handleThresholdConfirm}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </Popup> */}

      <Popup
        visible={capacityPopupVisible}
        onMaskClick={() => setCapacityPopupVisible(false)}
        position="bottom"
        bodyStyle={{
          borderRadius: "16px",
          margin: "0 12px 12px 12px",
          width: "calc(100% - 24px)",
          maxWidth: "calc(100% - 24px)",
        }}
      >
        <div className={styles.capacityPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>录像存档容量</div>
          </div>

          <div className={styles.popupBody}>
            <div className={styles.capacityInputContainer}>
              <Input
                type="number"
                className={styles.capacityInput}
                value={capacityInput}
                onChange={handleCapacityInput}
                onFocus={(e) => e.target.select()}
                placeholder="请输入容量"
                autoFocus
                clearable
              />
              <div
                className={styles.unitDisplay}
                onClick={() => setUnitSelectorVisible(true)}
              >
                {config.storageUnit}
                <img
                  src={isDarkMode ? polygonDark : polygon}
                  alt=""
                  className={styles.arrowIcon}
                />
              </div>
            </div>

            <div className={styles.popupButtons}>
              <Button
                className={styles.cancelButton}
                onClick={() => setCapacityPopupVisible(false)}
              >
                取消
              </Button>
              <Button
                className={styles.confirmButton}
                color="primary"
                onClick={handleCapacityConfirm}
              // loading={savingConfig}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </Popup>

      <UnitSelector
        visible={unitSelectorVisible}
        onClose={() => setUnitSelectorVisible(false)}
        value={config.storageUnit}
        options={unitOptions}
        onChange={handleUnitChange}
        title="选择单位"
      />
    </div>
  );
};

export default RecordingConfig;
